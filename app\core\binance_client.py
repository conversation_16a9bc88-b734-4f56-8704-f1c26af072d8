"""
Binance WebSocket và REST API client để lấy dữ liệu cryptocurrency real-time
"""
import json
import asyncio
import aiohttp
import websockets
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime, timezone
import time
from urllib.parse import urlencode

from app.config.settings import settings, WEBSOCKET_CONFIG, RATE_LIMITS
from app.utils.logger import LoggerMixin


class BinanceWebSocketClient(LoggerMixin):
    """
    Binance WebSocket client để streaming dữ liệu real-time

    Chức năng chính:
    - Kết nối WebSocket với Binance API
    - Nhận dữ liệu OHLCV, ticker, order book, trade real-time
    - Tự động reconnect khi mất kết nối
    - Quản lý multiple streams đồng thời
    """

    def __init__(self):
        # URL cơ sở cho Binance WebSocket
        self.base_url = settings.BINANCE_WS_URL

        # Dictionary lưu trữ các kết nối WebSocket active
        self.connections: Dict[str, websockets.WebSocketServerProtocol] = {}

        # Dictionary lưu trữ danh sách symbols đã subscribe cho mỗi stream
        self.subscriptions: Dict[str, List[str]] = {}

        # Dictionary lưu trữ callback functions cho mỗi stream
        self.callbacks: Dict[str, Callable] = {}

        # Dictionary theo dõi số lần thử reconnect cho mỗi stream
        self.reconnect_attempts: Dict[str, int] = {}

        # Flag kiểm tra trạng thái hoạt động của client
        self.is_running = False
        
    async def connect(self, stream_name: str, symbols: List[str], callback: Callable) -> bool:
        """
        Connect to Binance WebSocket stream
        
        Args:
            stream_name: Name of the stream (e.g., 'kline', 'ticker', 'depth')
            symbols: List of trading symbols
            callback: Callback function to handle incoming data
            
        Returns:
            bool: True if connection successful
        """
        try:
            # Build stream URL
            streams = self._build_streams(stream_name, symbols)
            url = f"{self.base_url}/{'/'.join(streams)}"
            
            self.logger.info(f"Connecting to Binance WebSocket: {stream_name}")
            self.logger.debug(f"WebSocket URL: {url}")
            
            # Connect to WebSocket
            websocket = await websockets.connect(
                url,
                ping_interval=WEBSOCKET_CONFIG["ping_interval"],
                ping_timeout=WEBSOCKET_CONFIG["ping_timeout"],
                close_timeout=WEBSOCKET_CONFIG["close_timeout"],
                max_size=WEBSOCKET_CONFIG["max_size"],
                max_queue=WEBSOCKET_CONFIG["max_queue"],
                read_limit=WEBSOCKET_CONFIG["read_limit"],
                write_limit=WEBSOCKET_CONFIG["write_limit"]
            )
            
            # Store connection info
            self.connections[stream_name] = websocket
            self.subscriptions[stream_name] = symbols
            self.callbacks[stream_name] = callback
            self.reconnect_attempts[stream_name] = 0
            
            self.logger.info(f"Successfully connected to {stream_name} stream")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to connect to {stream_name} stream: {e}")
            return False
    
    def _build_streams(self, stream_name: str, symbols: List[str]) -> List[str]:
        """Build stream names for WebSocket subscription"""
        streams = []
        
        for symbol in symbols:
            symbol_lower = symbol.lower()
            
            if stream_name == "kline":
                # Kline/Candlestick streams for different timeframes
                timeframes = ["1m", "5m", "15m", "1h", "4h", "1d"]
                for tf in timeframes:
                    streams.append(f"{symbol_lower}@kline_{tf}")
                    
            elif stream_name == "ticker":
                # 24hr ticker statistics
                streams.append(f"{symbol_lower}@ticker")
                
            elif stream_name == "depth":
                # Order book depth
                streams.append(f"{symbol_lower}@depth20@100ms")
                
            elif stream_name == "trade":
                # Individual trade data
                streams.append(f"{symbol_lower}@trade")
                
            elif stream_name == "miniTicker":
                # Mini ticker (price only)
                streams.append(f"{symbol_lower}@miniTicker")
        
        return streams
    
    async def listen(self, stream_name: str) -> None:
        """Listen for messages on a specific stream"""
        if stream_name not in self.connections:
            self.logger.error(f"No connection found for stream: {stream_name}")
            return
        
        websocket = self.connections[stream_name]
        callback = self.callbacks[stream_name]
        
        try:
            self.logger.info(f"Starting to listen on {stream_name} stream")
            
            async for message in websocket:
                try:
                    # Parse JSON message
                    data = json.loads(message)
                    
                    # Add timestamp if not present
                    if "E" not in data:
                        data["E"] = int(time.time() * 1000)
                    
                    # Call the callback function
                    await callback(stream_name, data)
                    
                except json.JSONDecodeError as e:
                    self.logger.error(f"Failed to parse JSON message: {e}")
                    continue
                    
                except Exception as e:
                    self.logger.error(f"Error in callback for {stream_name}: {e}")
                    continue
                    
        except websockets.exceptions.ConnectionClosed:
            self.logger.warning(f"WebSocket connection closed for {stream_name}")
            await self._handle_reconnection(stream_name)
            
        except Exception as e:
            self.logger.error(f"Error listening to {stream_name}: {e}")
            await self._handle_reconnection(stream_name)
    
    async def _handle_reconnection(self, stream_name: str) -> None:
        """Handle WebSocket reconnection with exponential backoff"""
        if stream_name not in self.reconnect_attempts:
            self.reconnect_attempts[stream_name] = 0
        
        self.reconnect_attempts[stream_name] += 1
        
        if self.reconnect_attempts[stream_name] > settings.MAX_RECONNECT_ATTEMPTS:
            self.logger.error(f"Max reconnection attempts reached for {stream_name}")
            return
        
        # Exponential backoff: 1s, 2s, 4s, 8s, ..., max 60s
        delay = min(2 ** (self.reconnect_attempts[stream_name] - 1), 60)
        
        self.logger.info(f"Reconnecting to {stream_name} in {delay} seconds (attempt {self.reconnect_attempts[stream_name]})")
        await asyncio.sleep(delay)
        
        # Attempt reconnection
        symbols = self.subscriptions[stream_name]
        callback = self.callbacks[stream_name]
        
        success = await self.connect(stream_name, symbols, callback)
        if success:
            # Reset reconnection counter on successful connection
            self.reconnect_attempts[stream_name] = 0
            # Start listening again
            asyncio.create_task(self.listen(stream_name))
        else:
            # Try again
            await self._handle_reconnection(stream_name)
    
    async def disconnect(self, stream_name: Optional[str] = None) -> None:
        """Disconnect from WebSocket stream(s)"""
        if stream_name:
            # Disconnect specific stream
            if stream_name in self.connections:
                await self.connections[stream_name].close()
                del self.connections[stream_name]
                self.logger.info(f"Disconnected from {stream_name} stream")
        else:
            # Disconnect all streams
            for name, websocket in self.connections.items():
                await websocket.close()
                self.logger.info(f"Disconnected from {name} stream")
            self.connections.clear()
    
    async def start_all_streams(self, symbols: List[str], data_handler) -> None:
        """Start all required WebSocket streams"""
        self.is_running = True
        
        # Define streams to start
        streams = [
            ("kline", symbols),
            ("ticker", symbols),
            ("depth", symbols),
            ("trade", symbols)
        ]
        
        # Start all streams
        tasks = []
        for stream_name, stream_symbols in streams:
            success = await self.connect(stream_name, stream_symbols, data_handler)
            if success:
                task = asyncio.create_task(self.listen(stream_name))
                tasks.append(task)
        
        self.logger.info(f"Started {len(tasks)} WebSocket streams")
        
        # Wait for all tasks
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    
    async def stop_all_streams(self) -> None:
        """Stop all WebSocket streams"""
        self.is_running = False
        await self.disconnect()
        self.logger.info("All WebSocket streams stopped")


class BinanceRESTClient(LoggerMixin):
    """Binance REST API client for historical data and market info"""
    
    def __init__(self):
        self.base_url = settings.BINANCE_BASE_URL
        self.session: Optional[aiohttp.ClientSession] = None
        self.rate_limiter = RateLimiter(RATE_LIMITS["binance_rest"])
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def get_exchange_info(self) -> Dict[str, Any]:
        """Get exchange trading rules and symbol information"""
        endpoint = "/api/v3/exchangeInfo"
        return await self._make_request("GET", endpoint)
    
    async def get_klines(self, symbol: str, interval: str, limit: int = 500, 
                        start_time: Optional[int] = None, end_time: Optional[int] = None) -> List[List]:
        """
        Get historical kline/candlestick data
        
        Args:
            symbol: Trading symbol (e.g., 'BTCUSDT')
            interval: Kline interval (1m, 5m, 15m, 1h, 4h, 1d, etc.)
            limit: Number of klines to return (max 1000)
            start_time: Start time in milliseconds
            end_time: End time in milliseconds
        """
        endpoint = "/api/v3/klines"
        params = {
            "symbol": symbol.upper(),
            "interval": interval,
            "limit": min(limit, 1000)
        }
        
        if start_time:
            params["startTime"] = start_time
        if end_time:
            params["endTime"] = end_time
        
        return await self._make_request("GET", endpoint, params)
    
    async def get_24hr_ticker(self, symbol: Optional[str] = None) -> Dict[str, Any]:
        """Get 24hr ticker price change statistics"""
        endpoint = "/api/v3/ticker/24hr"
        params = {}
        
        if symbol:
            params["symbol"] = symbol.upper()
        
        return await self._make_request("GET", endpoint, params)
    
    async def get_order_book(self, symbol: str, limit: int = 100) -> Dict[str, Any]:
        """Get order book depth"""
        endpoint = "/api/v3/depth"
        params = {
            "symbol": symbol.upper(),
            "limit": limit
        }
        
        return await self._make_request("GET", endpoint, params)
    
    async def _make_request(self, method: str, endpoint: str, params: Optional[Dict] = None) -> Any:
        """Make HTTP request to Binance API"""
        if not self.session:
            raise RuntimeError("HTTP session not initialized. Use async context manager.")
        
        # Apply rate limiting
        await self.rate_limiter.acquire()
        
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method == "GET":
                if params:
                    url += f"?{urlencode(params)}"
                
                async with self.session.get(url) as response:
                    response.raise_for_status()
                    return await response.json()
            
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
                
        except aiohttp.ClientError as e:
            self.logger.error(f"HTTP request failed: {e}")
            raise
        except Exception as e:
            self.logger.error(f"Unexpected error in API request: {e}")
            raise


class RateLimiter:
    """Simple rate limiter for API requests"""
    
    def __init__(self, config: Dict[str, int]):
        self.requests_per_minute = config["requests_per_minute"]
        self.burst_limit = config["burst_limit"]
        self.request_times = []
        self.lock = asyncio.Lock()
    
    async def acquire(self) -> None:
        """Acquire permission to make a request"""
        async with self.lock:
            now = time.time()
            
            # Remove old requests (older than 1 minute)
            self.request_times = [t for t in self.request_times if now - t < 60]
            
            # Check if we can make a request
            if len(self.request_times) >= self.requests_per_minute:
                # Calculate wait time
                oldest_request = min(self.request_times)
                wait_time = 60 - (now - oldest_request)
                
                if wait_time > 0:
                    await asyncio.sleep(wait_time)
            
            # Record this request
            self.request_times.append(now)
