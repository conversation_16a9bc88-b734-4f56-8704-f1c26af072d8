"""
<PERSON><PERSON><PERSON> hình settings cho crypto trading bot
Ch<PERSON><PERSON> tất cả các thiết lập cần thiết cho ứng dụng
"""
import os
from typing import List, Optional
from pydantic import field_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """
    Lớp cấu hình chính cho ứng dụng
    Sử dụng Pydantic để validate và load từ environment variables
    """

    # === CẤU HÌNH DATABASE ===
    # URL kết nối PostgreSQL cho database chính
    DATABASE_URL: str = "postgresql://postgres:admin@localhost:5432/crypto_trading_bot"
    # URL kết nối PostgreSQL cho database test
    TEST_DATABASE_URL: str = "postgresql://postgres:admin@localhost:5432/crypto_trading_bot_test"
    # URL kết nối Redis cho cache và session
    REDIS_URL: str = "redis://localhost:6379/0"

    # === API KEYS (TÙY CHỌN) ===
    # API key Binance (không bắt buộc cho public endpoints)
    BINANCE_API_KEY: Optional[str] = None
    BINANCE_SECRET_KEY: Optional[str] = None
    # API key CoinGecko (không bắt buộc cho free tier)
    COINGECKO_API_KEY: Optional[str] = None
    # API key CryptoCompare (không bắt buộc cho free tier)
    CRYPTOCOMPARE_API_KEY: Optional[str] = None

    # === CẤU HÌNH ỨNG DỤNG ===
    # Secret key để mã hóa JWT tokens (PHẢI THAY ĐỔI TRONG PRODUCTION)
    SECRET_KEY: str = "your-super-secret-key-change-in-production"
    # Thuật toán mã hóa JWT
    ALGORITHM: str = "HS256"
    # Thời gian hết hạn access token (phút)
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # === CẤU HÌNH MÔI TRƯỜNG ===
    # Môi trường chạy: development, production, testing
    ENVIRONMENT: str = "development"
    # Bật/tắt debug mode
    DEBUG: bool = True
    # Mức độ logging: DEBUG, INFO, WARNING, ERROR
    LOG_LEVEL: str = "INFO"

    # === CẤU HÌNH TRADING ===
    # Phần trăm rủi ro mặc định cho mỗi lệnh (2% = 0.02)
    DEFAULT_RISK_PERCENTAGE: float = 2.0
    # Số lượng positions tối đa có thể mở cùng lúc
    MAX_POSITIONS: int = 5
    # Điểm tin cậy tối thiểu để thực hiện trade (0.7 = 70%)
    MIN_CONFIDENCE_SCORE: float = 0.7

    # === CẤU HÌNH THU THẬP DỮ LIỆU ===
    # Khoảng thời gian thu thập dữ liệu (giây)
    DATA_COLLECTION_INTERVAL: int = 60
    # Thời gian chờ trước khi thử reconnect WebSocket (giây)
    WEBSOCKET_RECONNECT_DELAY: int = 5
    # Số lần thử reconnect tối đa
    MAX_RECONNECT_ATTEMPTS: int = 10
    # Khoảng thời gian gửi heartbeat để duy trì kết nối (giây)
    HEARTBEAT_INTERVAL: int = 30

    # === DANH SÁCH TRADING PAIRS HỖ TRỢ ===
    # Chuỗi các symbols cách nhau bởi dấu phẩy
    SUPPORTED_SYMBOLS: str = "BTCUSDT,ETHUSDT,BNBUSDT,ADAUSDT,SOLUSDT,MATICUSDT,DOTUSDT,AVAXUSDT,LINKUSDT,UNIUSDT"

    # === CẤU HÌNH RATE LIMITING ===
    # Giới hạn requests per minute cho Binance API
    BINANCE_RATE_LIMIT: int = 1200
    # Giới hạn requests per minute cho CoinGecko API
    COINGECKO_RATE_LIMIT: int = 50
    # Số lượng WebSocket connections tối đa
    WEBSOCKET_MAX_CONNECTIONS: int = 5

    # === CẤU HÌNH VALIDATION DỮ LIỆU ===
    # Ngưỡng thay đổi giá tối đa được chấp nhận (10% = 0.10)
    PRICE_CHANGE_THRESHOLD: float = 0.10
    # Thời gian tối đa dữ liệu được coi là "cũ" (giây)
    DATA_STALENESS_THRESHOLD: int = 5
    # Volume tối thiểu để coi là hợp lệ (USDT)
    MIN_VOLUME_THRESHOLD: float = 1000.0

    # === API ENDPOINTS ===
    # URL cơ sở cho Binance REST API
    BINANCE_BASE_URL: str = "https://api.binance.com"
    # URL cho Binance WebSocket
    BINANCE_WS_URL: str = "wss://stream.binance.com:9443/ws"
    # URL cơ sở cho CoinGecko API
    COINGECKO_BASE_URL: str = "https://api.coingecko.com/api/v3"
    # URL cho Kraken WebSocket (dự phòng)
    KRAKEN_WS_URL: str = "wss://ws.kraken.com"
    
    @field_validator('SUPPORTED_SYMBOLS')
    @classmethod
    def parse_symbols(cls, v):
        """Chuyển đổi chuỗi symbols thành list"""
        if isinstance(v, str):
            return [symbol.strip().upper() for symbol in v.split(',')]
        return v

    @property
    def supported_symbols_list(self) -> List[str]:
        """Lấy danh sách symbols được hỗ trợ dưới dạng list"""
        if isinstance(self.SUPPORTED_SYMBOLS, str):
            return [symbol.strip().upper() for symbol in self.SUPPORTED_SYMBOLS.split(',')]
        return self.SUPPORTED_SYMBOLS

    @property
    def is_production(self) -> bool:
        """Kiểm tra xem có đang chạy trong môi trường production không"""
        return self.ENVIRONMENT.lower() == "production"

    @property
    def is_development(self) -> bool:
        """Kiểm tra xem có đang chạy trong môi trường development không"""
        return self.ENVIRONMENT.lower() == "development"
    
    model_config = {
        # File chứa environment variables
        "env_file": ".env",
        # Phân biệt chữ hoa/thường cho tên biến
        "case_sensitive": True
    }


# === INSTANCE SETTINGS TOÀN CỤC ===
settings = Settings()


# === CẤU HÌNH RATE LIMITING CHO CÁC API ===
RATE_LIMITS = {
    # Giới hạn cho Binance REST API
    "binance_rest": {
        "requests_per_minute": settings.BINANCE_RATE_LIMIT,  # Requests per minute
        "weight_limit": 1200,  # Weight limit theo Binance
        "burst_limit": 100     # Số requests có thể gửi liên tiếp
    },
    # Giới hạn cho CoinGecko API
    "coingecko": {
        "requests_per_minute": settings.COINGECKO_RATE_LIMIT,  # Requests per minute
        "monthly_limit": 30000,  # Giới hạn hàng tháng
        "burst_limit": 10        # Số requests có thể gửi liên tiếp
    },
    # Giới hạn cho WebSocket connections
    "websocket_connections": {
        "max_connections": settings.WEBSOCKET_MAX_CONNECTIONS,  # Số connections tối đa
        "max_streams_per_connection": 1024  # Số streams tối đa per connection
    }
}


# WebSocket Configuration
WEBSOCKET_CONFIG = {
    "ping_interval": settings.HEARTBEAT_INTERVAL,
    "ping_timeout": 10,
    "close_timeout": 10,
    "max_size": 2**20,  # 1MB
    "max_queue": 32,
    "read_limit": 2**16,  # 64KB
    "write_limit": 2**16,  # 64KB
}


# Data Validation Rules
VALIDATION_RULES = {
    "price_change_threshold": settings.PRICE_CHANGE_THRESHOLD,
    "data_staleness_threshold": settings.DATA_STALENESS_THRESHOLD,
    "min_volume_threshold": settings.MIN_VOLUME_THRESHOLD,
    "required_ohlcv_fields": ["open", "high", "low", "close", "volume"],
    "required_ticker_fields": ["symbol", "price", "timestamp"],
    "required_orderbook_fields": ["bids", "asks", "timestamp"]
}


# Logging Configuration
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "default": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        },
        "detailed": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s - %(message)s",
        },
    },
    "handlers": {
        "default": {
            "formatter": "default",
            "class": "logging.StreamHandler",
            "stream": "ext://sys.stdout",
        },
        "file": {
            "formatter": "detailed",
            "class": "logging.handlers.RotatingFileHandler",
            "filename": "logs/trading_bot.log",
            "maxBytes": 10485760,  # 10MB
            "backupCount": 5,
        },
    },
    "loggers": {
        "": {
            "level": settings.LOG_LEVEL,
            "handlers": ["default", "file"],
        },
    },
}
