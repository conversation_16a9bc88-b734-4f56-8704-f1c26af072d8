"""
Configuration settings for the crypto trading bot
"""
import os
from typing import List, Optional
from pydantic import BaseSettings, validator


class Settings(BaseSettings):
    """Application settings"""
    
    # Database
    DATABASE_URL: str = "postgresql://postgres:admin@localhost:5432/crypto_trading_bot"
    TEST_DATABASE_URL: str = "postgresql://postgres:admin@localhost:5432/crypto_trading_bot_test"
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # API Keys (Optional)
    BINANCE_API_KEY: Optional[str] = None
    BINANCE_SECRET_KEY: Optional[str] = None
    COINGECKO_API_KEY: Optional[str] = None
    CRYPTOCOMPARE_API_KEY: Optional[str] = None
    
    # Application
    SECRET_KEY: str = "your-super-secret-key-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # Environment
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    LOG_LEVEL: str = "INFO"
    
    # Trading Settings
    DEFAULT_RISK_PERCENTAGE: float = 2.0
    MAX_POSITIONS: int = 5
    MIN_CONFIDENCE_SCORE: float = 0.7
    
    # Data Collection
    DATA_COLLECTION_INTERVAL: int = 60  # seconds
    WEBSOCKET_RECONNECT_DELAY: int = 5  # seconds
    MAX_RECONNECT_ATTEMPTS: int = 10
    HEARTBEAT_INTERVAL: int = 30  # seconds
    
    # Supported Trading Pairs
    SUPPORTED_SYMBOLS: str = "BTCUSDT,ETHUSDT,BNBUSDT,ADAUSDT,SOLUSDT,MATICUSDT,DOTUSDT,AVAXUSDT,LINKUSDT,UNIUSDT"
    
    # Rate Limiting
    BINANCE_RATE_LIMIT: int = 1200  # requests per minute
    COINGECKO_RATE_LIMIT: int = 50  # requests per minute
    WEBSOCKET_MAX_CONNECTIONS: int = 5
    
    # Data Validation
    PRICE_CHANGE_THRESHOLD: float = 0.10  # 10% max price change
    DATA_STALENESS_THRESHOLD: int = 5  # seconds
    MIN_VOLUME_THRESHOLD: float = 1000.0  # USDT
    
    # API Endpoints
    BINANCE_BASE_URL: str = "https://api.binance.com"
    BINANCE_WS_URL: str = "wss://stream.binance.com:9443/ws"
    COINGECKO_BASE_URL: str = "https://api.coingecko.com/api/v3"
    KRAKEN_WS_URL: str = "wss://ws.kraken.com"
    
    @validator('SUPPORTED_SYMBOLS')
    def parse_symbols(cls, v):
        """Parse comma-separated symbols into list"""
        if isinstance(v, str):
            return [symbol.strip().upper() for symbol in v.split(',')]
        return v
    
    @property
    def supported_symbols_list(self) -> List[str]:
        """Get supported symbols as list"""
        if isinstance(self.SUPPORTED_SYMBOLS, str):
            return [symbol.strip().upper() for symbol in self.SUPPORTED_SYMBOLS.split(',')]
        return self.SUPPORTED_SYMBOLS
    
    @property
    def is_production(self) -> bool:
        """Check if running in production"""
        return self.ENVIRONMENT.lower() == "production"
    
    @property
    def is_development(self) -> bool:
        """Check if running in development"""
        return self.ENVIRONMENT.lower() == "development"
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()


# API Rate Limiting Configuration
RATE_LIMITS = {
    "binance_rest": {
        "requests_per_minute": settings.BINANCE_RATE_LIMIT,
        "weight_limit": 1200,
        "burst_limit": 100
    },
    "coingecko": {
        "requests_per_minute": settings.COINGECKO_RATE_LIMIT,
        "monthly_limit": 30000,
        "burst_limit": 10
    },
    "websocket_connections": {
        "max_connections": settings.WEBSOCKET_MAX_CONNECTIONS,
        "max_streams_per_connection": 1024
    }
}


# WebSocket Configuration
WEBSOCKET_CONFIG = {
    "ping_interval": settings.HEARTBEAT_INTERVAL,
    "ping_timeout": 10,
    "close_timeout": 10,
    "max_size": 2**20,  # 1MB
    "max_queue": 32,
    "read_limit": 2**16,  # 64KB
    "write_limit": 2**16,  # 64KB
}


# Data Validation Rules
VALIDATION_RULES = {
    "price_change_threshold": settings.PRICE_CHANGE_THRESHOLD,
    "data_staleness_threshold": settings.DATA_STALENESS_THRESHOLD,
    "min_volume_threshold": settings.MIN_VOLUME_THRESHOLD,
    "required_ohlcv_fields": ["open", "high", "low", "close", "volume"],
    "required_ticker_fields": ["symbol", "price", "timestamp"],
    "required_orderbook_fields": ["bids", "asks", "timestamp"]
}


# Logging Configuration
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "default": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        },
        "detailed": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s - %(message)s",
        },
    },
    "handlers": {
        "default": {
            "formatter": "default",
            "class": "logging.StreamHandler",
            "stream": "ext://sys.stdout",
        },
        "file": {
            "formatter": "detailed",
            "class": "logging.handlers.RotatingFileHandler",
            "filename": "logs/trading_bot.log",
            "maxBytes": 10485760,  # 10MB
            "backupCount": 5,
        },
    },
    "loggers": {
        "": {
            "level": settings.LOG_LEVEL,
            "handlers": ["default", "file"],
        },
    },
}
