# Tài li<PERSON><PERSON> thuật - Bot AI Trading Signal Cryptocurrency

## 1. CHIẾN LƯỢC PHÂN TÍCH KỸ THUẬT

### 1.1 Danh sách Chỉ báo <PERSON>ỹ thuật

#### A. Momentum Indicators
- **RSI (Relative Strength Index)**
  - <PERSON> <PERSON><PERSON>: 14 periods
  - Ngưỡng oversold: < 30
  - Ngưỡng overbought: > 70
  - Ngưỡng trung tính: 40-60

- **Stochastic Oscillator**
  - %K period: 14
  - %D period: 3
  - Smooth: 3
  - Oversold: < 20, Overbought: > 80

#### B. Trend Following Indicators
- **MACD (Moving Average Convergence Divergence)**
  - Fast EMA: 12 periods
  - Slow EMA: 26 periods
  - Signal line: 9 periods EMA
  - Histogram threshold: ±0.0001

- **Moving Averages**
  - EMA 9, 21, 50, 200
  - SMA 20, 50, 100, 200
  - Trend confirmation: EMA 9 > EMA 21 > EMA 50

#### C. Volatility Indicators
- **Bollinger Bands**
  - Period: 20
  - Standard deviation: 2.0
  - Squeeze threshold: Band width < 0.1 * price

- **ATR (Average True Range)**
  - Period: 14
  - Volatility classification:
    - Low: ATR < 2% of price
    - Medium: 2% ≤ ATR < 4%
    - High: ATR ≥ 4%

#### D. Volume Indicators
- **Volume Weighted Average Price (VWAP)**
- **On-Balance Volume (OBV)**
- **Volume Rate of Change**
  - Period: 10

### 1.2 Cấu hình Multiple Timeframe Analysis

#### Primary Timeframes:
- **Scalping**: 1m, 5m
- **Day Trading**: 15m, 1h
- **Swing Trading**: 4h, 1d

#### Timeframe Hierarchy:
1. **Higher Timeframe (HTF)**: Xác định trend chính
2. **Lower Timeframe (LTF)**: Tìm entry point chính xác

## 2. LOGIC SINH TÍN HIỆU

### 2.1 Điều kiện Tín hiệu LONG (Mua)

#### A. Điều kiện Bắt buộc:
1. **Trend Confirmation**:
   - EMA 9 > EMA 21 > EMA 50 (trên timeframe chính)
   - Price > EMA 200 (uptrend dài hạn)

2. **Momentum Confirmation**:
   - RSI > 50 và đang tăng
   - MACD line > Signal line
   - MACD histogram > 0

3. **Entry Triggers**:
   - Price pullback về EMA 21 và bounce
   - RSI oversold (< 30) và divergence bullish
   - Bollinger Bands squeeze breakout lên trên

#### B. Điều kiện Tăng cường:
- Volume tăng > 150% average volume
- Stochastic %K cross above %D trong vùng oversold
- OBV tăng cùng chiều với price

### 2.2 Điều kiện Tín hiệu SHORT (Bán khống)

#### A. Điều kiện Bắt buộc:
1. **Trend Confirmation**:
   - EMA 9 < EMA 21 < EMA 50 (trên timeframe chính)
   - Price < EMA 200 (downtrend dài hạn)

2. **Momentum Confirmation**:
   - RSI < 50 và đang giảm
   - MACD line < Signal line
   - MACD histogram < 0

3. **Entry Triggers**:
   - Price rally về EMA 21 và reject
   - RSI overbought (> 70) và divergence bearish
   - Bollinger Bands squeeze breakout xuống dưới

#### B. Điều kiện Tăng cường:
- Volume tăng > 150% average volume
- Stochastic %K cross below %D trong vùng overbought
- OBV giảm cùng chiều với price decline

### 2.3 Tính toán Confidence Score

#### Công thức Confidence Score (0-100):
```
Base Score = 50

Trend Alignment:
+ 15 points: All EMAs aligned with signal direction
+ 10 points: Primary EMAs (9,21) aligned
+ 5 points: Only EMA 9 aligned

Momentum Confirmation:
+ 10 points: RSI + MACD + Stochastic aligned
+ 7 points: RSI + MACD aligned
+ 5 points: Only RSI aligned

Volume Confirmation:
+ 10 points: Volume > 200% average
+ 7 points: Volume > 150% average
+ 3 points: Volume > 100% average

Technical Pattern:
+ 10 points: Clear divergence pattern
+ 7 points: Support/Resistance break
+ 5 points: Moving average bounce/rejection

Market Structure:
+ 5 points: Higher timeframe alignment
+ 3 points: No conflicting signals

Maximum Score: 100
Minimum Trading Score: 70
```

### 2.4 Quy tắc Lọc False Signals

#### A. Market Condition Filters:
- Không trade trong 30 phút trước/sau major news
- Tránh trade khi volatility quá thấp (ATR < 1%)
- Không trade khi spread > 0.1%

#### B. Technical Filters:
- Loại bỏ signals trong sideways market (ADX < 25)
- Tránh trade khi price ở giữa Bollinger Bands
- Không trade khi RSI ở vùng 45-55 (indecision zone)

## 3. QUẢN LÝ RỦI RO

### 3.1 Entry Point Calculation

#### A. Long Entry:
- **Aggressive**: Market price khi signal xuất hiện
- **Conservative**: Pullback về EMA 21 + 0.1% buffer
- **Breakout**: Break above resistance + 0.05%

#### B. Short Entry:
- **Aggressive**: Market price khi signal xuất hiện  
- **Conservative**: Rally về EMA 21 - 0.1% buffer
- **Breakdown**: Break below support - 0.05%

### 3.2 Stop Loss Calculation

#### A. ATR-based Stop Loss:
```
Long Stop Loss = Entry Price - (ATR * 2.0)
Short Stop Loss = Entry Price + (ATR * 2.0)
```

#### B. Technical Stop Loss:
- **Long**: Below recent swing low hoặc EMA 50
- **Short**: Above recent swing high hoặc EMA 50

#### C. Percentage Stop Loss:
- **High Volatility coins**: 3-5%
- **Medium Volatility coins**: 2-3%  
- **Low Volatility coins**: 1-2%

### 3.3 Take Profit Calculation

#### A. Risk-Reward Based:
```
Minimum R:R Ratio = 1:2
Target 1 = Entry + (Stop Loss Distance * 2)
Target 2 = Entry + (Stop Loss Distance * 3)
Target 3 = Entry + (Stop Loss Distance * 5)
```

#### B. Technical Take Profit:
- **Long**: Next resistance level, Bollinger upper band
- **Short**: Next support level, Bollinger lower band

### 3.4 Position Sizing

#### A. Fixed Percentage Risk:
```
Risk per Trade = Account Balance * Risk Percentage (1-3%)
Position Size = Risk Amount / Stop Loss Distance
```

#### B. Volatility Adjusted:
```
Volatility Multiplier = 1 / (ATR / Price)
Adjusted Position Size = Base Position Size * Volatility Multiplier
Maximum Position Size = 10% of Account Balance
```

## 4. TIMEFRAMES VÀ MARKETS

### 4.1 Supported Timeframes
- **1m**: Scalping signals (confidence > 85)
- **5m**: Short-term signals (confidence > 80)  
- **15m**: Intraday signals (confidence > 75)
- **1h**: Swing signals (confidence > 70)
- **4h**: Position signals (confidence > 70)
- **1d**: Long-term signals (confidence > 65)

### 4.2 Supported Cryptocurrency Pairs

#### Tier 1 (Highest Priority):
- BTC/USDT, ETH/USDT, BNB/USDT

#### Tier 2 (Medium Priority):  
- ADA/USDT, SOL/USDT, MATIC/USDT, DOT/USDT
- AVAX/USDT, LINK/USDT, UNI/USDT

#### Tier 3 (Lower Priority):
- ATOM/USDT, FTM/USDT, NEAR/USDT, ALGO/USDT

### 4.3 Multiple Timeframe Analysis Rules

#### A. Trend Confirmation:
- Higher timeframe trend phải align với signal direction
- Minimum 2 timeframes confirmation required

#### B. Entry Timing:
- Signal từ higher timeframe
- Entry timing từ lower timeframe
- Exit management trên multiple timeframes

## 5. ĐỊNH DẠNG OUTPUT

### 5.1 JSON Schema cho Trading Signal

```json
{
  "signal_id": "string (UUID)",
  "timestamp": "ISO 8601 datetime",
  "symbol": "string (e.g., BTC/USDT)",
  "timeframe": "string (1m, 5m, 15m, 1h, 4h, 1d)",
  "signal_type": "LONG | SHORT",
  "confidence_score": "number (0-100)",
  "entry_price": "number",
  "stop_loss": "number", 
  "take_profit_levels": [
    {
      "level": 1,
      "price": "number",
      "percentage": "number (% of position to close)"
    }
  ],
  "risk_reward_ratio": "number",
  "position_size_percentage": "number (% of account)",
  "market_conditions": {
    "volatility": "LOW | MEDIUM | HIGH",
    "trend_strength": "number (0-100)",
    "volume_profile": "LOW | NORMAL | HIGH"
  },
  "technical_analysis": {
    "indicators": {
      "rsi": "number",
      "macd": {
        "macd_line": "number",
        "signal_line": "number", 
        "histogram": "number"
      },
      "moving_averages": {
        "ema_9": "number",
        "ema_21": "number",
        "ema_50": "number"
      },
      "bollinger_bands": {
        "upper": "number",
        "middle": "number", 
        "lower": "number"
      }
    },
    "pattern_detected": "string (optional)",
    "support_resistance": {
      "support": "number",
      "resistance": "number"
    }
  },
  "reasoning": "string (explanation of signal logic)",
  "validity_period": "number (minutes)",
  "status": "ACTIVE | FILLED | CANCELLED | EXPIRED"
}
```

### 5.2 Ví dụ Signal Output

```json
{
  "signal_id": "550e8400-e29b-41d4-a716-************",
  "timestamp": "2024-01-15T10:30:00Z",
  "symbol": "BTC/USDT", 
  "timeframe": "1h",
  "signal_type": "LONG",
  "confidence_score": 78,
  "entry_price": 42500.00,
  "stop_loss": 41800.00,
  "take_profit_levels": [
    {"level": 1, "price": 43900.00, "percentage": 50},
    {"level": 2, "price": 44600.00, "percentage": 30}, 
    {"level": 3, "price": 46000.00, "percentage": 20}
  ],
  "risk_reward_ratio": 2.0,
  "position_size_percentage": 2.5,
  "market_conditions": {
    "volatility": "MEDIUM",
    "trend_strength": 72,
    "volume_profile": "HIGH"
  },
  "technical_analysis": {
    "indicators": {
      "rsi": 58.5,
      "macd": {
        "macd_line": 125.4,
        "signal_line": 98.7,
        "histogram": 26.7
      },
      "moving_averages": {
        "ema_9": 42350.0,
        "ema_21": 42100.0, 
        "ema_50": 41800.0
      },
      "bollinger_bands": {
        "upper": 43200.0,
        "middle": 42000.0,
        "lower": 40800.0
      }
    },
    "pattern_detected": "Bullish divergence on RSI",
    "support_resistance": {
      "support": 41800.0,
      "resistance": 43500.0
    }
  },
  "reasoning": "Strong bullish momentum with RSI divergence, MACD crossover above signal line, price bouncing from EMA 21 support with high volume confirmation",
  "validity_period": 240,
  "status": "ACTIVE"
}
```

---

**Lưu ý**: Tài liệu này sẽ được cập nhật và tinh chỉnh trong quá trình phát triển dựa trên kết quả backtesting và performance thực tế.
