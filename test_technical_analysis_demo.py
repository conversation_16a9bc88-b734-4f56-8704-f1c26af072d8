"""
Demo script để test Technical Analysis Module với dữ liệu real-time

Script này sẽ:
1. <PERSON><PERSON>t nối với Binance WebSocket
2. <PERSON><PERSON>h toán các technical indicators real-time
3. Tạo trading signals dựa trên indicators
4. <PERSON><PERSON><PERSON> thị kết quả phân tích
"""
import asyncio
import json
from datetime import datetime
from typing import List

from app.core.trading_engine import TradingEngine, TradingConfig
from app.core.signal_generator import IndicatorConfig, TechnicalSignal
from app.core.technical_analysis import SignalType
from app.config.settings import settings
from app.utils.logger import setup_logging, get_logger

# Thiết lập logging
setup_logging("INFO")
logger = get_logger(__name__)


class TechnicalAnalysisDemo:
    """Demo class cho Technical Analysis"""
    
    def __init__(self):
        """Khởi tạo demo"""
        self.trading_engine = None
        self.signal_count = 0
        self.demo_duration = 120  # 2 phút demo
        
    async def signal_callback(self, signal: TechnicalSignal) -> None:
        """
        Callback function để xử lý signals mới
        
        Args:
            signal: Trading signal mới
        """
        self.signal_count += 1
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # Tạo emoji cho signal type
        emoji_map = {
            SignalType.STRONG_BUY: "🚀",
            SignalType.BUY: "📈",
            SignalType.HOLD: "⏸️",
            SignalType.SELL: "📉",
            SignalType.STRONG_SELL: "💥"
        }
        
        emoji = emoji_map.get(signal.signal_type, "❓")
        
        logger.info(f"\n{'='*60}")
        logger.info(f"{emoji} TRADING SIGNAL #{self.signal_count}")
        logger.info(f"Time: {timestamp}")
        logger.info(f"Symbol: {signal.symbol}")
        logger.info(f"Timeframe: {signal.timeframe}")
        logger.info(f"Signal: {signal.signal_type.value}")
        logger.info(f"Confidence: {signal.confidence:.1%}")
        logger.info(f"Price: ${signal.price:.2f}")
        
        if signal.stop_loss:
            logger.info(f"Stop Loss: ${signal.stop_loss:.2f}")
        if signal.take_profit:
            logger.info(f"Take Profit: ${signal.take_profit:.2f}")
        if signal.risk_reward_ratio:
            logger.info(f"Risk/Reward: 1:{signal.risk_reward_ratio:.2f}")
        
        logger.info(f"Reasoning: {signal.reasoning}")
        
        # Hiển thị key indicators
        if signal.indicators:
            logger.info("Key Indicators:")
            if signal.indicators.get('rsi'):
                logger.info(f"  RSI: {signal.indicators['rsi']:.1f}")
            if signal.indicators.get('ema_9') and signal.indicators.get('ema_21'):
                logger.info(f"  EMA 9: ${signal.indicators['ema_9']:.2f}")
                logger.info(f"  EMA 21: ${signal.indicators['ema_21']:.2f}")
            if signal.indicators.get('macd_line') and signal.indicators.get('macd_signal'):
                logger.info(f"  MACD: {signal.indicators['macd_line']:.4f}")
                logger.info(f"  MACD Signal: {signal.indicators['macd_signal']:.4f}")
        
        logger.info(f"{'='*60}\n")
    
    async def display_indicators_periodically(self) -> None:
        """Hiển thị indicators định kỳ"""
        while self.trading_engine and self.trading_engine.is_running:
            await asyncio.sleep(30)  # Mỗi 30 giây
            
            timestamp = datetime.now().strftime("%H:%M:%S")
            logger.info(f"\n📊 INDICATOR UPDATE - {timestamp}")
            
            # Hiển thị indicators cho BTC và ETH
            for symbol in ["BTCUSDT", "ETHUSDT"]:
                indicators = self.trading_engine.get_latest_indicators("1h")
                
                if indicators:
                    logger.info(f"\n{symbol} Indicators:")
                    logger.info(f"  Price: ${indicators.get('price', 0):.2f}")
                    
                    if indicators.get('rsi'):
                        rsi = indicators['rsi']
                        rsi_status = "Oversold" if rsi < 30 else "Overbought" if rsi > 70 else "Neutral"
                        logger.info(f"  RSI: {rsi:.1f} ({rsi_status})")
                    
                    if indicators.get('ema_9') and indicators.get('ema_21'):
                        ema_9 = indicators['ema_9']
                        ema_21 = indicators['ema_21']
                        trend = "Bullish" if ema_9 > ema_21 else "Bearish"
                        logger.info(f"  EMA Trend: {trend} (9: ${ema_9:.2f}, 21: ${ema_21:.2f})")
                    
                    if indicators.get('macd_line') and indicators.get('macd_signal'):
                        macd_line = indicators['macd_line']
                        macd_signal = indicators['macd_signal']
                        macd_trend = "Bullish" if macd_line > macd_signal else "Bearish"
                        logger.info(f"  MACD: {macd_trend} ({macd_line:.4f} vs {macd_signal:.4f})")
                    
                    if indicators.get('bb_percent_b'):
                        bb_percent = indicators['bb_percent_b'] * 100
                        bb_position = "Upper" if bb_percent > 80 else "Lower" if bb_percent < 20 else "Middle"
                        logger.info(f"  Bollinger: {bb_position} ({bb_percent:.1f}%)")
    
    async def display_engine_status(self) -> None:
        """Hiển thị trạng thái engine định kỳ"""
        while self.trading_engine and self.trading_engine.is_running:
            await asyncio.sleep(60)  # Mỗi phút
            
            status = self.trading_engine.get_engine_status()
            timestamp = datetime.now().strftime("%H:%M:%S")
            
            logger.info(f"\n🔧 ENGINE STATUS - {timestamp}")
            logger.info(f"  Running: {status['is_running']}")
            logger.info(f"  Symbols: {status['symbols_count']}")
            logger.info(f"  Active Signals: {status['active_signals_count']}")
            logger.info(f"  Total Signals: {status['total_signals_generated']}")
            logger.info(f"  Data Buffers: {status['data_buffers_count']}")
            
            # Connection status
            conn_status = status.get('connection_status', {})
            logger.info("  Connections:")
            for source, connected in conn_status.items():
                status_emoji = "✅" if connected else "❌"
                logger.info(f"    {source}: {status_emoji}")
    
    async def run_demo(self) -> None:
        """Chạy demo chính"""
        logger.info("🚀 BẮT ĐẦU TECHNICAL ANALYSIS DEMO")
        logger.info(f"Demo sẽ chạy trong {self.demo_duration} giây")
        logger.info("=" * 60)
        
        try:
            # Cấu hình trading engine
            config = TradingConfig(
                symbols=["BTCUSDT", "ETHUSDT"],  # Chỉ test 2 symbols chính
                timeframes=["1h"],  # Chỉ 1 timeframe để đơn giản
                indicator_config=IndicatorConfig(
                    min_confidence=0.6,  # Giảm threshold để dễ tạo signals
                    max_signals_per_hour=10
                ),
                signal_callbacks=[self.signal_callback]
            )
            
            # Khởi tạo trading engine
            self.trading_engine = TradingEngine(config)
            
            # Bắt đầu engine
            await self.trading_engine.start()
            
            # Bắt đầu các tasks hiển thị
            indicator_task = asyncio.create_task(self.display_indicators_periodically())
            status_task = asyncio.create_task(self.display_engine_status())
            
            # Chạy demo trong thời gian quy định
            logger.info(f"✅ Trading Engine đã khởi động. Đang theo dõi signals...")
            await asyncio.sleep(self.demo_duration)
            
            # Dừng tasks
            indicator_task.cancel()
            status_task.cancel()
            
            # Dừng engine
            await self.trading_engine.stop()
            
            # Hiển thị tóm tắt
            await self.display_summary()
            
        except KeyboardInterrupt:
            logger.info("\n⏹️  Demo bị dừng bởi người dùng")
        except Exception as e:
            logger.error(f"\n💥 Lỗi trong demo: {e}")
        finally:
            if self.trading_engine:
                await self.trading_engine.stop()
    
    async def display_summary(self) -> None:
        """Hiển thị tóm tắt kết quả demo"""
        logger.info("\n" + "=" * 60)
        logger.info("📊 TÓM TẮT KẾT QUẢ DEMO")
        logger.info("=" * 60)
        
        if self.trading_engine:
            # Lấy signal history
            signal_history = self.trading_engine.get_signal_history(50)
            
            logger.info(f"Tổng số signals tạo ra: {len(signal_history)}")
            
            if signal_history:
                # Thống kê theo signal type
                signal_stats = {}
                for signal in signal_history:
                    signal_type = signal.signal_type.value
                    signal_stats[signal_type] = signal_stats.get(signal_type, 0) + 1
                
                logger.info("Phân bố signals:")
                for signal_type, count in signal_stats.items():
                    logger.info(f"  {signal_type}: {count}")
                
                # Confidence trung bình
                avg_confidence = sum(s.confidence for s in signal_history) / len(signal_history)
                logger.info(f"Confidence trung bình: {avg_confidence:.1%}")
                
                # Hiển thị 3 signals gần nhất
                logger.info("\n3 Signals gần nhất:")
                for i, signal in enumerate(signal_history[-3:], 1):
                    timestamp = datetime.fromtimestamp(signal.timestamp / 1000).strftime("%H:%M:%S")
                    logger.info(f"  {i}. {timestamp} - {signal.symbol} {signal.signal_type.value} "
                              f"(Confidence: {signal.confidence:.1%})")
            
            # Engine status cuối cùng
            status = self.trading_engine.get_engine_status()
            logger.info(f"\nData buffers tạo ra: {status['data_buffers_count']}")
            logger.info(f"Connection status: {status['connection_status']}")
        
        logger.info("\n🎉 DEMO HOÀN THÀNH!")
        logger.info("Technical Analysis Module hoạt động thành công với dữ liệu real-time")


async def main():
    """Hàm main để chạy demo"""
    demo = TechnicalAnalysisDemo()
    await demo.run_demo()


if __name__ == "__main__":
    """Chạy demo khi file được execute trực tiếp"""
    print("=" * 70)
    print("🔬 DEMO TECHNICAL ANALYSIS MODULE")
    print("=" * 70)
    print("Demo này sẽ:")
    print("✓ Kết nối Binance WebSocket real-time")
    print("✓ Tính toán RSI, MACD, Moving Averages, Bollinger Bands")
    print("✓ Tạo trading signals với confidence scoring")
    print("✓ Hiển thị indicators và signals real-time")
    print("✓ Chạy trong 2 phút với BTC/USDT và ETH/USDT")
    print("=" * 70)
    
    # Chạy demo
    asyncio.run(main())
