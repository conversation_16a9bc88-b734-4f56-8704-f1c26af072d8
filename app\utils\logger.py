"""
Logging configuration and utilities
"""
import os
import logging
import logging.config
from typing import Optional
from app.config.settings import LOGGING_CONFIG, settings


def setup_logging(log_level: Optional[str] = None) -> None:
    """Setup logging configuration"""
    
    # Create logs directory if it doesn't exist
    os.makedirs("logs", exist_ok=True)
    
    # Override log level if provided
    if log_level:
        LOGGING_CONFIG["loggers"][""]["level"] = log_level.upper()
    
    # Apply logging configuration
    logging.config.dictConfig(LOGGING_CONFIG)


def get_logger(name: str) -> logging.Logger:
    """Get logger instance with specified name"""
    return logging.getLogger(name)


class LoggerMixin:
    """Mixin class to add logging capability to any class"""
    
    @property
    def logger(self) -> logging.Logger:
        """Get logger for this class"""
        return get_logger(self.__class__.__name__)


# Setup logging on module import
setup_logging()

# Create module logger
logger = get_logger(__name__)
