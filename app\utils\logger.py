"""
<PERSON><PERSON><PERSON> hình logging và các tiện ích liên quan
Quản lý việc ghi log cho toàn bộ ứng dụng trading bot
"""
import os
import logging
import logging.config
from typing import Optional
from app.config.settings import LOGGING_CONFIG, settings


def setup_logging(log_level: Optional[str] = None) -> None:
    """
    Thiết lập cấu hình logging cho ứng dụng

    Args:
        log_level: <PERSON><PERSON><PERSON> độ log tùy chọn (DEBUG, INFO, WARNING, ERROR)
    """

    # Tạo thư mục logs nếu chưa tồn tại
    os.makedirs("logs", exist_ok=True)

    # Ghi đè log level nếu được cung cấp
    if log_level:
        LOGGING_CONFIG["loggers"][""]["level"] = log_level.upper()

    # Áp dụng cấu hình logging
    logging.config.dictConfig(LOGGING_CONFIG)


def get_logger(name: str) -> logging.Logger:
    """
    Lấy logger instance với tên đ<PERSON>ợc chỉ định

    Args:
        name: <PERSON><PERSON><PERSON> củ<PERSON> logger (thường là __name__ của module)

    Returns:
        logging.Logger: Instance của logger
    """
    return logging.getLogger(name)


class LoggerMixin:
    """
    Mixin class để thêm khả năng logging vào bất kỳ class nào
    Sử dụng: class MyClass(LoggerMixin): ...
    Sau đó có thể dùng: self.logger.info("message")
    """

    @property
    def logger(self) -> logging.Logger:
        """Lấy logger cho class này với tên class làm tên logger"""
        return get_logger(self.__class__.__name__)


# Thiết lập logging khi import module
setup_logging()

# Tạo logger cho module này
logger = get_logger(__name__)
