"""
Trading Engine - Engine chính tích hợp Data Collection và Signal Generation

Module này kết nối data collector với signal generator để tạo ra
một hệ thống trading hoàn chỉnh với real-time analysis
"""
import asyncio
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass

from app.core.data_collector import DataCollector, MarketData
from app.core.signal_generator import SignalGenerator, IndicatorConfig, TechnicalSignal
from app.config.settings import settings
from app.utils.logger import LoggerMixin


@dataclass
class TradingConfig:
    """Cấu hình cho Trading Engine"""
    # Symbols để theo dõi
    symbols: List[str] = None
    
    # Timeframes để phân tích
    timeframes: List[str] = None
    
    # Indicator configuration
    indicator_config: IndicatorConfig = None
    
    # Signal callbacks
    signal_callbacks: List[Callable] = None
    
    def __post_init__(self):
        """Khởi tạo default values"""
        if self.symbols is None:
            self.symbols = settings.supported_symbols_list
        
        if self.timeframes is None:
            self.timeframes = ["1h", "4h"]  # Default timeframes
        
        if self.indicator_config is None:
            self.indicator_config = IndicatorConfig()
        
        if self.signal_callbacks is None:
            self.signal_callbacks = []


class TradingEngine(LoggerMixin):
    """
    Trading Engine chính
    
    Tích hợp data collection và signal generation để tạo ra
    hệ thống trading hoàn chỉnh với real-time analysis
    """
    
    def __init__(self, config: Optional[TradingConfig] = None):
        """
        Khởi tạo Trading Engine
        
        Args:
            config: Cấu hình trading engine
        """
        self.config = config or TradingConfig()
        
        # Khởi tạo components
        self.data_collector = DataCollector()
        
        # Tạo signal generators cho mỗi timeframe
        self.signal_generators: Dict[str, SignalGenerator] = {}
        for timeframe in self.config.timeframes:
            self.signal_generators[timeframe] = SignalGenerator(self.config.indicator_config)
        
        # Lưu trữ signals
        self.active_signals: List[TechnicalSignal] = []
        self.signal_history: List[TechnicalSignal] = []
        
        # Tracking data cho mỗi symbol/timeframe
        self.market_data_buffers: Dict[str, List[MarketData]] = {}
        
        # Status
        self.is_running = False
        
        self.logger.info(f"Trading Engine khởi tạo với {len(self.config.symbols)} symbols, "
                        f"{len(self.config.timeframes)} timeframes")
    
    async def start(self) -> None:
        """Bắt đầu trading engine"""
        if self.is_running:
            self.logger.warning("Trading Engine đã đang chạy")
            return
        
        self.logger.info("Bắt đầu Trading Engine...")
        
        # Đăng ký callback với data collector
        self.data_collector.data_callbacks.append(self._handle_market_data)
        
        # Bắt đầu data collection
        await self.data_collector.start(self.config.symbols)
        
        self.is_running = True
        self.logger.info("Trading Engine đã khởi động thành công")
    
    async def stop(self) -> None:
        """Dừng trading engine"""
        if not self.is_running:
            return
        
        self.logger.info("Đang dừng Trading Engine...")
        
        # Dừng data collection
        await self.data_collector.binance_ws.stop_all_streams()
        
        self.is_running = False
        self.logger.info("Trading Engine đã dừng")
    
    async def _handle_market_data(self, market_data: MarketData) -> None:
        """
        Xử lý market data từ data collector
        
        Args:
            market_data: Dữ liệu thị trường mới
        """
        try:
            # Lưu trữ market data
            self._store_market_data(market_data)
            
            # Xử lý cho từng timeframe
            for timeframe in self.config.timeframes:
                # Chuyển đổi data cho timeframe cụ thể
                timeframe_data = self._convert_to_timeframe(market_data, timeframe)
                
                if timeframe_data:
                    # Tạo signal nếu có
                    signal = self.signal_generators[timeframe].process_market_data(timeframe_data)
                    
                    if signal:
                        await self._handle_new_signal(signal)
            
        except Exception as e:
            self.logger.error(f"Lỗi khi xử lý market data: {e}")
    
    def _store_market_data(self, market_data: MarketData) -> None:
        """
        Lưu trữ market data vào buffer
        
        Args:
            market_data: Dữ liệu thị trường
        """
        key = f"{market_data.symbol}_{market_data.timeframe or 'tick'}"
        
        if key not in self.market_data_buffers:
            self.market_data_buffers[key] = []
        
        self.market_data_buffers[key].append(market_data)
        
        # Giữ buffer trong giới hạn (1000 data points)
        if len(self.market_data_buffers[key]) > 1000:
            self.market_data_buffers[key] = self.market_data_buffers[key][-500:]
    
    def _convert_to_timeframe(self, market_data: MarketData, target_timeframe: str) -> Optional[MarketData]:
        """
        Chuyển đổi market data sang timeframe cụ thể
        
        Args:
            market_data: Dữ liệu gốc
            target_timeframe: Timeframe đích
            
        Returns:
            MarketData cho timeframe đích hoặc None
        """
        # Nếu data đã đúng timeframe
        if market_data.timeframe == target_timeframe:
            return market_data
        
        # Nếu là ticker data, sử dụng price làm close
        if market_data.timeframe is None and market_data.price:
            return MarketData(
                symbol=market_data.symbol,
                timestamp=market_data.timestamp,
                source=market_data.source,
                close=market_data.price,
                volume=market_data.volume,
                timeframe=target_timeframe
            )
        
        # Nếu có OHLCV data, sử dụng trực tiếp
        if all([market_data.open, market_data.high, market_data.low, market_data.close]):
            return MarketData(
                symbol=market_data.symbol,
                timestamp=market_data.timestamp,
                source=market_data.source,
                open=market_data.open,
                high=market_data.high,
                low=market_data.low,
                close=market_data.close,
                volume=market_data.volume,
                timeframe=target_timeframe
            )
        
        return None
    
    async def _handle_new_signal(self, signal: TechnicalSignal) -> None:
        """
        Xử lý signal mới được tạo
        
        Args:
            signal: Signal mới
        """
        # Thêm vào danh sách active signals
        self.active_signals.append(signal)
        self.signal_history.append(signal)
        
        # Giữ active signals trong giới hạn
        if len(self.active_signals) > 50:
            self.active_signals = self.active_signals[-25:]
        
        # Giữ signal history trong giới hạn
        if len(self.signal_history) > 1000:
            self.signal_history = self.signal_history[-500:]
        
        # Log signal
        self.logger.info(
            f"🎯 NEW SIGNAL: {signal.signal_type.value} {signal.symbol} "
            f"({signal.timeframe}) - Confidence: {signal.confidence:.2f} - "
            f"Price: ${signal.price:.2f}"
        )
        
        # Gọi callbacks
        for callback in self.config.signal_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(signal)
                else:
                    callback(signal)
            except Exception as e:
                self.logger.error(f"Lỗi trong signal callback: {e}")
    
    def get_active_signals(self, symbol: Optional[str] = None, 
                          timeframe: Optional[str] = None) -> List[TechnicalSignal]:
        """
        Lấy danh sách active signals
        
        Args:
            symbol: Lọc theo symbol (optional)
            timeframe: Lọc theo timeframe (optional)
            
        Returns:
            Danh sách signals
        """
        signals = self.active_signals
        
        if symbol:
            signals = [s for s in signals if s.symbol == symbol]
        
        if timeframe:
            signals = [s for s in signals if s.timeframe == timeframe]
        
        return signals
    
    def get_signal_history(self, limit: int = 20) -> List[TechnicalSignal]:
        """
        Lấy lịch sử signals
        
        Args:
            limit: Số lượng signals tối đa
            
        Returns:
            Danh sách signals gần nhất
        """
        return self.signal_history[-limit:] if self.signal_history else []
    
    def get_latest_indicators(self, timeframe: str = "1h") -> Optional[dict]:
        """
        Lấy giá trị indicators mới nhất
        
        Args:
            timeframe: Timeframe để lấy indicators
            
        Returns:
            Dictionary chứa indicator values
        """
        if timeframe in self.signal_generators:
            indicators = self.signal_generators[timeframe].get_latest_indicators()
            return indicators.__dict__ if indicators else None
        
        return None
    
    def get_market_data_buffer(self, symbol: str, timeframe: str = "tick") -> List[MarketData]:
        """
        Lấy buffer market data
        
        Args:
            symbol: Trading symbol
            timeframe: Timeframe
            
        Returns:
            Danh sách market data
        """
        key = f"{symbol}_{timeframe}"
        return self.market_data_buffers.get(key, [])
    
    def get_engine_status(self) -> dict:
        """
        Lấy trạng thái của trading engine
        
        Returns:
            Dictionary chứa thông tin status
        """
        return {
            "is_running": self.is_running,
            "symbols_count": len(self.config.symbols),
            "timeframes_count": len(self.config.timeframes),
            "active_signals_count": len(self.active_signals),
            "total_signals_generated": len(self.signal_history),
            "data_buffers_count": len(self.market_data_buffers),
            "connection_status": self.data_collector.connection_status
        }
