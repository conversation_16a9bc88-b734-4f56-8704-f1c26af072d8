"""
Signal Generation Engine - Engine tạo tín hiệu trading

Module nà<PERSON> kết hợp multiple technical indicators để tạo ra trading signals
với confidence score và risk management recommendations
"""
import time
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict

from app.core.technical_analysis import (
    BaseIndicator, TechnicalSignal, SignalType,
    RSI, MACD, MovingAverage, BollingerBands,
    VolumeMovingAverage, VolumeRateOfChange, OnBalanceVolume
)
from app.core.data_collector import MarketData
from app.config.settings import settings
from app.utils.logger import LoggerMixin


@dataclass
class IndicatorConfig:
    """Cấu hình cho các technical indicators"""
    # RSI settings
    rsi_period: int = 14
    rsi_overbought: float = 70.0
    rsi_oversold: float = 30.0
    
    # Moving Averages settings
    ma_fast_period: int = 9
    ma_medium_period: int = 21
    ma_slow_period: int = 50
    ma_long_period: int = 200
    
    # MACD settings
    macd_fast: int = 12
    macd_slow: int = 26
    macd_signal: int = 9
    
    # Bollinger Bands settings
    bb_period: int = 20
    bb_std_multiplier: float = 2.0
    
    # Volume settings
    volume_ma_period: int = 20
    volume_roc_period: int = 10
    
    # Signal generation settings
    min_confidence: float = 0.7
    max_signals_per_hour: int = 5


@dataclass
class IndicatorValues:
    """Giá trị hiện tại của tất cả indicators"""
    # Price và Volume
    price: float
    volume: float
    
    # RSI
    rsi: Optional[float] = None
    
    # Moving Averages
    ema_9: Optional[float] = None
    ema_21: Optional[float] = None
    ema_50: Optional[float] = None
    ema_200: Optional[float] = None
    
    # MACD
    macd_line: Optional[float] = None
    macd_signal: Optional[float] = None
    macd_histogram: Optional[float] = None
    
    # Bollinger Bands
    bb_upper: Optional[float] = None
    bb_middle: Optional[float] = None
    bb_lower: Optional[float] = None
    bb_percent_b: Optional[float] = None
    
    # Volume indicators
    volume_ma: Optional[float] = None
    volume_roc: Optional[float] = None
    obv: Optional[float] = None


class SignalGenerator(LoggerMixin):
    """
    Signal Generation Engine chính
    
    Kết hợp multiple technical indicators để tạo trading signals
    với confidence scoring và risk management
    """
    
    def __init__(self, config: Optional[IndicatorConfig] = None):
        """
        Khởi tạo Signal Generator
        
        Args:
            config: Cấu hình indicators (sử dụng default nếu None)
        """
        self.config = config or IndicatorConfig()
        
        # Khởi tạo indicators
        self.indicators = self._initialize_indicators()
        
        # Lưu trữ signals đã tạo
        self.generated_signals: List[TechnicalSignal] = []
        
        # Tracking cho rate limiting
        self.signal_timestamps: List[int] = []
        
        # Lưu trữ indicator values cho analysis
        self.indicator_history: List[IndicatorValues] = []
        
    def _initialize_indicators(self) -> Dict[str, BaseIndicator]:
        """
        Khởi tạo tất cả technical indicators
        
        Returns:
            Dictionary chứa tất cả indicators
        """
        indicators = {
            # RSI
            'rsi': RSI(
                period=self.config.rsi_period,
                overbought=self.config.rsi_overbought,
                oversold=self.config.rsi_oversold
            ),
            
            # Moving Averages
            'ema_9': MovingAverage(self.config.ma_fast_period, "EMA"),
            'ema_21': MovingAverage(self.config.ma_medium_period, "EMA"),
            'ema_50': MovingAverage(self.config.ma_slow_period, "EMA"),
            'ema_200': MovingAverage(self.config.ma_long_period, "EMA"),
            
            # MACD
            'macd': MACD(
                fast_period=self.config.macd_fast,
                slow_period=self.config.macd_slow,
                signal_period=self.config.macd_signal
            ),
            
            # Bollinger Bands
            'bb': BollingerBands(
                period=self.config.bb_period,
                std_multiplier=self.config.bb_std_multiplier
            ),
            
            # Volume indicators
            'volume_ma': VolumeMovingAverage(self.config.volume_ma_period),
            'volume_roc': VolumeRateOfChange(self.config.volume_roc_period),
            'obv': OnBalanceVolume()
        }
        
        self.logger.info(f"Đã khởi tạo {len(indicators)} technical indicators")
        return indicators
    
    def process_market_data(self, market_data: MarketData) -> Optional[TechnicalSignal]:
        """
        Xử lý market data và tạo trading signal nếu có
        
        Args:
            market_data: Dữ liệu thị trường từ data collector
            
        Returns:
            TechnicalSignal nếu có signal, None nếu không
        """
        try:
            # Kiểm tra rate limiting
            if not self._check_rate_limit():
                return None
            
            # Tính toán tất cả indicators
            indicator_values = self._calculate_indicators(market_data)
            
            # Lưu trữ indicator values
            self.indicator_history.append(indicator_values)
            
            # Giữ history trong giới hạn
            if len(self.indicator_history) > 1000:
                self.indicator_history = self.indicator_history[-500:]
            
            # Phân tích và tạo signal
            signal = self._analyze_and_generate_signal(market_data, indicator_values)
            
            if signal:
                self.generated_signals.append(signal)
                self.signal_timestamps.append(int(time.time()))
                
                self.logger.info(
                    f"Tạo signal {signal.signal_type.value} cho {signal.symbol} "
                    f"với confidence {signal.confidence:.2f}"
                )
            
            return signal
            
        except Exception as e:
            self.logger.error(f"Lỗi khi xử lý market data: {e}")
            return None
    
    def _calculate_indicators(self, market_data: MarketData) -> IndicatorValues:
        """
        Tính toán tất cả technical indicators
        
        Args:
            market_data: Dữ liệu thị trường
            
        Returns:
            IndicatorValues chứa giá trị tất cả indicators
        """
        price = market_data.close or market_data.price or 0.0
        volume = market_data.volume or 0.0
        
        # Tính RSI
        rsi = self.indicators['rsi'].calculate(price)
        
        # Tính Moving Averages
        ema_9 = self.indicators['ema_9'].calculate(price)
        ema_21 = self.indicators['ema_21'].calculate(price)
        ema_50 = self.indicators['ema_50'].calculate(price)
        ema_200 = self.indicators['ema_200'].calculate(price)
        
        # Tính MACD
        macd_result = self.indicators['macd'].calculate(price)
        macd_line = macd_result.macd_line if macd_result else None
        macd_signal = macd_result.signal_line if macd_result else None
        macd_histogram = macd_result.histogram if macd_result else None
        
        # Tính Bollinger Bands
        bb_result = self.indicators['bb'].calculate(price)
        bb_upper = bb_result.upper_band if bb_result else None
        bb_middle = bb_result.middle_band if bb_result else None
        bb_lower = bb_result.lower_band if bb_result else None
        bb_percent_b = bb_result.percent_b if bb_result else None
        
        # Tính Volume indicators
        volume_ma = self.indicators['volume_ma'].calculate(volume) if volume > 0 else None
        volume_roc = self.indicators['volume_roc'].calculate(volume) if volume > 0 else None
        obv = self.indicators['obv'].calculate(price, volume) if volume > 0 else None
        
        return IndicatorValues(
            price=price,
            volume=volume,
            rsi=rsi,
            ema_9=ema_9,
            ema_21=ema_21,
            ema_50=ema_50,
            ema_200=ema_200,
            macd_line=macd_line,
            macd_signal=macd_signal,
            macd_histogram=macd_histogram,
            bb_upper=bb_upper,
            bb_middle=bb_middle,
            bb_lower=bb_lower,
            bb_percent_b=bb_percent_b,
            volume_ma=volume_ma,
            volume_roc=volume_roc,
            obv=obv
        )
    
    def _analyze_and_generate_signal(self, market_data: MarketData, 
                                   indicators: IndicatorValues) -> Optional[TechnicalSignal]:
        """
        Phân tích indicators và tạo trading signal
        
        Args:
            market_data: Dữ liệu thị trường
            indicators: Giá trị các indicators
            
        Returns:
            TechnicalSignal nếu có signal mạnh
        """
        # Kiểm tra điều kiện cơ bản
        if not self._has_sufficient_data(indicators):
            return None
        
        # Phân tích trend
        trend_score = self._analyze_trend(indicators)
        
        # Phân tích momentum
        momentum_score = self._analyze_momentum(indicators)
        
        # Phân tích volume
        volume_score = self._analyze_volume(indicators)
        
        # Phân tích volatility
        volatility_score = self._analyze_volatility(indicators)
        
        # Tính tổng confidence score
        total_score = (trend_score + momentum_score + volume_score + volatility_score) / 4
        
        # Xác định signal type
        signal_type = self._determine_signal_type(total_score, indicators)
        
        # Chỉ tạo signal nếu confidence đủ cao
        if abs(total_score) < self.config.min_confidence:
            return None
        
        # Tạo signal với risk management
        return self._create_signal(
            market_data=market_data,
            signal_type=signal_type,
            confidence=abs(total_score),
            indicators=indicators
        )

    def _has_sufficient_data(self, indicators: IndicatorValues) -> bool:
        """
        Kiểm tra xem có đủ dữ liệu để tạo signal không

        Args:
            indicators: Giá trị indicators

        Returns:
            True nếu có đủ dữ liệu cần thiết
        """
        required_indicators = [
            indicators.rsi,
            indicators.ema_9,
            indicators.ema_21,
            indicators.macd_line,
            indicators.macd_signal
        ]

        return all(ind is not None for ind in required_indicators)

    def _analyze_trend(self, indicators: IndicatorValues) -> float:
        """
        Phân tích trend dựa trên Moving Averages

        Args:
            indicators: Giá trị indicators

        Returns:
            Trend score (-1.0 đến 1.0)
        """
        score = 0.0

        # Kiểm tra EMA alignment
        if all([indicators.ema_9, indicators.ema_21, indicators.ema_50]):
            if indicators.ema_9 > indicators.ema_21 > indicators.ema_50:
                score += 0.4  # Strong uptrend
            elif indicators.ema_9 < indicators.ema_21 < indicators.ema_50:
                score -= 0.4  # Strong downtrend
            elif indicators.ema_9 > indicators.ema_21:
                score += 0.2  # Weak uptrend
            elif indicators.ema_9 < indicators.ema_21:
                score -= 0.2  # Weak downtrend

        # Kiểm tra price vs EMAs
        if indicators.ema_21:
            if indicators.price > indicators.ema_21:
                score += 0.2
            else:
                score -= 0.2

        # Kiểm tra long-term trend
        if indicators.ema_200:
            if indicators.price > indicators.ema_200:
                score += 0.2  # Above long-term trend
            else:
                score -= 0.2  # Below long-term trend

        return max(-1.0, min(1.0, score))

    def _analyze_momentum(self, indicators: IndicatorValues) -> float:
        """
        Phân tích momentum dựa trên RSI và MACD

        Args:
            indicators: Giá trị indicators

        Returns:
            Momentum score (-1.0 đến 1.0)
        """
        score = 0.0

        # RSI analysis - Mở rộng điều kiện
        if indicators.rsi is not None:
            if indicators.rsi < self.config.rsi_oversold:
                score += 0.4  # Tăng từ 0.3 lên 0.4
            elif indicators.rsi > self.config.rsi_overbought:
                score -= 0.4  # Tăng từ 0.3 lên 0.4
            elif indicators.rsi < 45:  # Thêm điều kiện: RSI < 45 = bullish
                score += 0.2
            elif indicators.rsi > 55:  # Thêm điều kiện: RSI > 55 = bearish
                score -= 0.2
            elif 45 <= indicators.rsi <= 55:  # Giảm neutral zone
                score += 0.1

        # MACD analysis
        if indicators.macd_line is not None and indicators.macd_signal is not None:
            if indicators.macd_line > indicators.macd_signal:
                score += 0.2  # Bullish MACD
            else:
                score -= 0.2  # Bearish MACD

            # MACD histogram
            if indicators.macd_histogram is not None:
                if indicators.macd_histogram > 0:
                    score += 0.1
                else:
                    score -= 0.1

        # Kiểm tra MACD crossover từ history
        if len(self.indicator_history) >= 2:
            current = indicators
            previous = self.indicator_history[-2]

            if (current.macd_line and current.macd_signal and
                previous.macd_line and previous.macd_signal):

                # Bullish crossover
                if (previous.macd_line <= previous.macd_signal and
                    current.macd_line > current.macd_signal):
                    score += 0.3

                # Bearish crossover
                elif (previous.macd_line >= previous.macd_signal and
                      current.macd_line < current.macd_signal):
                    score -= 0.3

        return max(-1.0, min(1.0, score))

    def _analyze_volume(self, indicators: IndicatorValues) -> float:
        """
        Phân tích volume để xác nhận signal

        Args:
            indicators: Giá trị indicators

        Returns:
            Volume score (-0.5 đến 0.5)
        """
        score = 0.0

        # So sánh volume với moving average
        if indicators.volume_ma and indicators.volume > 0:
            volume_ratio = indicators.volume / indicators.volume_ma

            if volume_ratio > 1.5:
                score += 0.3  # High volume confirmation
            elif volume_ratio > 1.2:
                score += 0.2  # Above average volume
            elif volume_ratio < 0.8:
                score -= 0.1  # Low volume - weak signal

        # Volume Rate of Change
        if indicators.volume_roc is not None:
            if indicators.volume_roc > 50:
                score += 0.2  # Strong volume increase
            elif indicators.volume_roc < -30:
                score -= 0.1  # Volume decrease

        return max(-0.5, min(0.5, score))

    def _analyze_volatility(self, indicators: IndicatorValues) -> float:
        """
        Phân tích volatility dựa trên Bollinger Bands

        Args:
            indicators: Giá trị indicators

        Returns:
            Volatility score (-0.5 đến 0.5)
        """
        score = 0.0

        if indicators.bb_percent_b is not None:
            # %B analysis
            if indicators.bb_percent_b < 0.2:
                score += 0.2  # Near lower band - potential buy
            elif indicators.bb_percent_b > 0.8:
                score -= 0.2  # Near upper band - potential sell
            elif 0.4 <= indicators.bb_percent_b <= 0.6:
                score += 0.1  # Middle of bands - stable

        # Bollinger Bands squeeze detection
        bb_indicator = self.indicators['bb']
        if hasattr(bb_indicator, 'is_squeeze') and bb_indicator.is_squeeze():
            score += 0.1  # Squeeze - potential breakout

        return max(-0.5, min(0.5, score))

    def _determine_signal_type(self, score: float, indicators: IndicatorValues) -> SignalType:
        """
        Xác định loại signal dựa trên score

        Args:
            score: Tổng điểm từ analysis
            indicators: Giá trị indicators

        Returns:
            SignalType
        """
        # Threshold rất thấp để dễ tạo signals
        if score >= 0.5:  # STRONG_BUY
            return SignalType.STRONG_BUY
        elif score >= 0.3:  # BUY - Giảm xuống 0.3
            return SignalType.BUY
        elif score <= -0.5:  # STRONG_SELL
            return SignalType.STRONG_SELL
        elif score <= -0.3:  # SELL - Tăng lên -0.3
            return SignalType.SELL
        else:
            return SignalType.HOLD

    def _create_signal(self, market_data: MarketData, signal_type: SignalType,
                      confidence: float, indicators: IndicatorValues) -> TechnicalSignal:
        """
        Tạo TechnicalSignal với risk management

        Args:
            market_data: Dữ liệu thị trường
            signal_type: Loại signal
            confidence: Độ tin cậy
            indicators: Giá trị indicators

        Returns:
            TechnicalSignal hoàn chỉnh
        """
        price = indicators.price

        # Tính stop loss và take profit
        stop_loss, take_profit = self._calculate_risk_levels(price, signal_type, indicators)

        # Tính risk-reward ratio
        risk_reward_ratio = None
        if stop_loss and take_profit:
            if signal_type in [SignalType.BUY, SignalType.STRONG_BUY]:
                risk = abs(price - stop_loss)
                reward = abs(take_profit - price)
            else:
                risk = abs(stop_loss - price)
                reward = abs(price - take_profit)

            if risk > 0:
                risk_reward_ratio = reward / risk

        # Tạo reasoning
        reasoning = self._generate_reasoning(signal_type, indicators)

        # Tạo indicators dict
        indicators_dict = {
            'rsi': indicators.rsi,
            'ema_9': indicators.ema_9,
            'ema_21': indicators.ema_21,
            'ema_50': indicators.ema_50,
            'macd_line': indicators.macd_line,
            'macd_signal': indicators.macd_signal,
            'macd_histogram': indicators.macd_histogram,
            'bb_percent_b': indicators.bb_percent_b,
            'volume_ratio': indicators.volume / indicators.volume_ma if indicators.volume_ma else None
        }

        return TechnicalSignal(
            symbol=market_data.symbol,
            timeframe=market_data.timeframe or "1h",
            signal_type=signal_type,
            confidence=confidence,
            timestamp=market_data.timestamp,
            price=price,
            indicators=indicators_dict,
            reasoning=reasoning,
            stop_loss=stop_loss,
            take_profit=take_profit,
            risk_reward_ratio=risk_reward_ratio
        )

    def _calculate_risk_levels(self, price: float, signal_type: SignalType,
                             indicators: IndicatorValues) -> Tuple[Optional[float], Optional[float]]:
        """
        Tính toán stop loss và take profit levels

        Args:
            price: Giá hiện tại
            signal_type: Loại signal
            indicators: Giá trị indicators

        Returns:
            Tuple (stop_loss, take_profit)
        """
        # Sử dụng ATR hoặc Bollinger Bands để tính risk levels
        if indicators.bb_upper and indicators.bb_lower:
            bb_range = indicators.bb_upper - indicators.bb_lower
            atr_estimate = bb_range / 4  # Ước tính ATR từ BB range
        else:
            atr_estimate = price * 0.02  # 2% default

        if signal_type in [SignalType.BUY, SignalType.STRONG_BUY]:
            # Long position
            stop_loss = price - (atr_estimate * 2)
            take_profit = price + (atr_estimate * 3)  # 1:1.5 risk-reward

            # Sử dụng EMA 21 làm dynamic stop nếu gần hơn
            if indicators.ema_21 and indicators.ema_21 > stop_loss:
                stop_loss = indicators.ema_21 * 0.995  # 0.5% buffer

        elif signal_type in [SignalType.SELL, SignalType.STRONG_SELL]:
            # Short position
            stop_loss = price + (atr_estimate * 2)
            take_profit = price - (atr_estimate * 3)

            # Sử dụng EMA 21 làm dynamic stop nếu gần hơn
            if indicators.ema_21 and indicators.ema_21 < stop_loss:
                stop_loss = indicators.ema_21 * 1.005  # 0.5% buffer
        else:
            return None, None

        return stop_loss, take_profit

    def _generate_reasoning(self, signal_type: SignalType, indicators: IndicatorValues) -> str:
        """
        Tạo reasoning cho signal

        Args:
            signal_type: Loại signal
            indicators: Giá trị indicators

        Returns:
            String mô tả lý do tạo signal
        """
        reasons = []

        # Trend analysis
        if indicators.ema_9 and indicators.ema_21:
            if indicators.ema_9 > indicators.ema_21:
                reasons.append("EMA 9 > EMA 21 (uptrend)")
            else:
                reasons.append("EMA 9 < EMA 21 (downtrend)")

        # RSI analysis
        if indicators.rsi:
            if indicators.rsi < 30:
                reasons.append(f"RSI oversold ({indicators.rsi:.1f})")
            elif indicators.rsi > 70:
                reasons.append(f"RSI overbought ({indicators.rsi:.1f})")
            else:
                reasons.append(f"RSI neutral ({indicators.rsi:.1f})")

        # MACD analysis
        if indicators.macd_line and indicators.macd_signal:
            if indicators.macd_line > indicators.macd_signal:
                reasons.append("MACD bullish")
            else:
                reasons.append("MACD bearish")

        # Volume analysis
        if indicators.volume_ma and indicators.volume:
            volume_ratio = indicators.volume / indicators.volume_ma
            if volume_ratio > 1.5:
                reasons.append("High volume confirmation")
            elif volume_ratio < 0.8:
                reasons.append("Low volume")

        return "; ".join(reasons)

    def _check_rate_limit(self) -> bool:
        """
        Kiểm tra rate limiting cho signal generation

        Returns:
            True nếu có thể tạo signal mới
        """
        current_time = int(time.time())

        # Xóa timestamps cũ hơn 1 giờ
        self.signal_timestamps = [
            ts for ts in self.signal_timestamps
            if current_time - ts < 3600
        ]

        # Kiểm tra số lượng signals trong 1 giờ qua
        return len(self.signal_timestamps) < self.config.max_signals_per_hour

    def get_latest_indicators(self) -> Optional[IndicatorValues]:
        """Lấy giá trị indicators mới nhất"""
        return self.indicator_history[-1] if self.indicator_history else None

    def get_signal_history(self, limit: int = 10) -> List[TechnicalSignal]:
        """
        Lấy lịch sử signals

        Args:
            limit: Số lượng signals tối đa

        Returns:
            Danh sách signals gần nhất
        """
        return self.generated_signals[-limit:] if self.generated_signals else []

    def reset(self) -> None:
        """Reset signal generator về trạng thái ban đầu"""
        for indicator in self.indicators.values():
            indicator.reset()

        self.generated_signals.clear()
        self.signal_timestamps.clear()
        self.indicator_history.clear()

        self.logger.info("Signal generator đã được reset")
