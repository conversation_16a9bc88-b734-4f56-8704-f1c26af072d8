"""
CoinGecko API client for fallback cryptocurrency data
"""
import aiohttp
import asyncio
import time
from typing import Dict, List, Optional, Any
from urllib.parse import urlencode

from app.config.settings import settings, RATE_LIMITS
from app.utils.logger import LoggerMixin


class CoinGeckoClient(LoggerMixin):
    """CoinGecko REST API client for cryptocurrency market data"""
    
    def __init__(self):
        self.base_url = settings.COINGECKO_BASE_URL
        self.session: Optional[aiohttp.ClientSession] = None
        self.rate_limiter = CoinGeckoRateLimiter(RATE_LIMITS["coingecko"])
        
        # Symbol mapping from Binance format to CoinGecko IDs
        self.symbol_mapping = {
            "BTCUSDT": "bitcoin",
            "ETHUSDT": "ethereum", 
            "BNBUSDT": "binancecoin",
            "ADAUSDT": "cardano",
            "SOLUSDT": "solana",
            "MATICUSDT": "matic-network",
            "DOTUSDT": "polkadot",
            "AVAXUSDT": "avalanche-2",
            "LINKUSDT": "chainlink",
            "UNIUSDT": "uniswap"
        }
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def get_simple_price(self, symbols: List[str], vs_currencies: List[str] = ["usd"]) -> Dict[str, Any]:
        """
        Get current prices for multiple cryptocurrencies
        
        Args:
            symbols: List of symbols in Binance format (e.g., ['BTCUSDT', 'ETHUSDT'])
            vs_currencies: List of fiat currencies to get prices in
            
        Returns:
            Dict with price data
        """
        # Convert symbols to CoinGecko IDs
        coin_ids = []
        for symbol in symbols:
            if symbol in self.symbol_mapping:
                coin_ids.append(self.symbol_mapping[symbol])
            else:
                self.logger.warning(f"Symbol {symbol} not found in mapping")
        
        if not coin_ids:
            return {}
        
        endpoint = "/simple/price"
        params = {
            "ids": ",".join(coin_ids),
            "vs_currencies": ",".join(vs_currencies),
            "include_24hr_change": "true",
            "include_24hr_vol": "true",
            "include_last_updated_at": "true"
        }
        
        data = await self._make_request("GET", endpoint, params)
        
        # Convert back to Binance symbol format
        result = {}
        for symbol in symbols:
            if symbol in self.symbol_mapping:
                coin_id = self.symbol_mapping[symbol]
                if coin_id in data:
                    result[symbol] = data[coin_id]
        
        return result
    
    async def get_market_data(self, symbols: List[str], vs_currency: str = "usd") -> List[Dict[str, Any]]:
        """
        Get comprehensive market data for cryptocurrencies
        
        Args:
            symbols: List of symbols in Binance format
            vs_currency: Fiat currency for prices
            
        Returns:
            List of market data dictionaries
        """
        # Convert symbols to CoinGecko IDs
        coin_ids = []
        for symbol in symbols:
            if symbol in self.symbol_mapping:
                coin_ids.append(self.symbol_mapping[symbol])
        
        if not coin_ids:
            return []
        
        endpoint = "/coins/markets"
        params = {
            "vs_currency": vs_currency,
            "ids": ",".join(coin_ids),
            "order": "market_cap_desc",
            "per_page": len(coin_ids),
            "page": 1,
            "sparkline": "false",
            "price_change_percentage": "1h,24h,7d"
        }
        
        return await self._make_request("GET", endpoint, params)
    
    async def get_historical_data(self, symbol: str, days: int = 30, vs_currency: str = "usd") -> Dict[str, Any]:
        """
        Get historical price data for a cryptocurrency
        
        Args:
            symbol: Symbol in Binance format
            days: Number of days of historical data
            vs_currency: Fiat currency for prices
            
        Returns:
            Historical price data
        """
        if symbol not in self.symbol_mapping:
            self.logger.error(f"Symbol {symbol} not supported")
            return {}
        
        coin_id = self.symbol_mapping[symbol]
        endpoint = f"/coins/{coin_id}/market_chart"
        params = {
            "vs_currency": vs_currency,
            "days": days,
            "interval": "hourly" if days <= 90 else "daily"
        }
        
        return await self._make_request("GET", endpoint, params)
    
    async def get_global_market_data(self) -> Dict[str, Any]:
        """Get global cryptocurrency market statistics"""
        endpoint = "/global"
        return await self._make_request("GET", endpoint)
    
    async def ping(self) -> Dict[str, Any]:
        """Ping CoinGecko API to check connectivity"""
        endpoint = "/ping"
        return await self._make_request("GET", endpoint)
    
    async def _make_request(self, method: str, endpoint: str, params: Optional[Dict] = None) -> Any:
        """Make HTTP request to CoinGecko API"""
        if not self.session:
            raise RuntimeError("HTTP session not initialized. Use async context manager.")
        
        # Apply rate limiting
        await self.rate_limiter.acquire()
        
        url = f"{self.base_url}{endpoint}"
        headers = {}
        
        # Add API key if available
        if settings.COINGECKO_API_KEY:
            headers["x-cg-demo-api-key"] = settings.COINGECKO_API_KEY
        
        try:
            if method == "GET":
                if params:
                    url += f"?{urlencode(params)}"
                
                self.logger.debug(f"Making CoinGecko API request: {url}")
                
                async with self.session.get(url, headers=headers) as response:
                    if response.status == 429:
                        # Rate limited
                        self.logger.warning("CoinGecko API rate limit exceeded")
                        retry_after = int(response.headers.get("Retry-After", 60))
                        await asyncio.sleep(retry_after)
                        return await self._make_request(method, endpoint, params)
                    
                    response.raise_for_status()
                    return await response.json()
            
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
                
        except aiohttp.ClientError as e:
            self.logger.error(f"CoinGecko API request failed: {e}")
            raise
        except Exception as e:
            self.logger.error(f"Unexpected error in CoinGecko API request: {e}")
            raise
    
    def get_binance_symbol_from_coingecko_id(self, coin_id: str) -> Optional[str]:
        """Convert CoinGecko coin ID back to Binance symbol format"""
        for symbol, cg_id in self.symbol_mapping.items():
            if cg_id == coin_id:
                return symbol
        return None
    
    async def validate_connection(self) -> bool:
        """Validate connection to CoinGecko API"""
        try:
            result = await self.ping()
            if result.get("gecko_says") == "(V3) To the Moon!":
                self.logger.info("CoinGecko API connection validated successfully")
                return True
            else:
                self.logger.warning("CoinGecko API ping returned unexpected response")
                return False
        except Exception as e:
            self.logger.error(f"CoinGecko API connection validation failed: {e}")
            return False


class CoinGeckoRateLimiter:
    """Rate limiter specifically for CoinGecko API"""
    
    def __init__(self, config: Dict[str, int]):
        self.requests_per_minute = config["requests_per_minute"]
        self.monthly_limit = config["monthly_limit"]
        self.burst_limit = config["burst_limit"]
        self.request_times = []
        self.monthly_requests = 0
        self.month_start = time.time()
        self.lock = asyncio.Lock()
    
    async def acquire(self) -> None:
        """Acquire permission to make a request"""
        async with self.lock:
            now = time.time()
            
            # Reset monthly counter if a month has passed
            if now - self.month_start > 30 * 24 * 3600:  # 30 days
                self.monthly_requests = 0
                self.month_start = now
            
            # Check monthly limit
            if self.monthly_requests >= self.monthly_limit:
                raise Exception("CoinGecko monthly API limit exceeded")
            
            # Remove old requests (older than 1 minute)
            self.request_times = [t for t in self.request_times if now - t < 60]
            
            # Check per-minute limit
            if len(self.request_times) >= self.requests_per_minute:
                # Calculate wait time
                oldest_request = min(self.request_times)
                wait_time = 60 - (now - oldest_request) + 1  # Add 1 second buffer
                
                if wait_time > 0:
                    await asyncio.sleep(wait_time)
            
            # Record this request
            self.request_times.append(now)
            self.monthly_requests += 1


# Utility functions for data conversion
def convert_coingecko_to_binance_format(cg_data: Dict[str, Any], symbol: str) -> Dict[str, Any]:
    """Convert CoinGecko data format to match Binance format"""
    if not cg_data:
        return {}
    
    # Extract price data
    price = cg_data.get("usd", 0)
    change_24h = cg_data.get("usd_24h_change", 0)
    volume_24h = cg_data.get("usd_24h_vol", 0)
    last_updated = cg_data.get("last_updated_at", int(time.time()))
    
    return {
        "symbol": symbol,
        "price": str(price),
        "priceChange": str(price * change_24h / 100) if change_24h else "0",
        "priceChangePercent": str(change_24h) if change_24h else "0",
        "volume": str(volume_24h),
        "lastPrice": str(price),
        "timestamp": last_updated * 1000,  # Convert to milliseconds
        "source": "coingecko"
    }


def convert_market_data_to_ticker_format(market_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Convert CoinGecko market data to ticker format"""
    tickers = []
    
    for coin in market_data:
        ticker = {
            "symbol": coin.get("symbol", "").upper() + "USDT",
            "price": str(coin.get("current_price", 0)),
            "priceChange": str(coin.get("price_change_24h", 0)),
            "priceChangePercent": str(coin.get("price_change_percentage_24h", 0)),
            "volume": str(coin.get("total_volume", 0)),
            "marketCap": str(coin.get("market_cap", 0)),
            "lastUpdated": coin.get("last_updated", ""),
            "source": "coingecko"
        }
        tickers.append(ticker)
    
    return tickers
