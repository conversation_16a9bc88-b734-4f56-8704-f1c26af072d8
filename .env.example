# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/crypto_trading_bot
TEST_DATABASE_URL=postgresql://username:password@localhost:5432/crypto_trading_bot_test

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# API Keys (Optional - for premium features)
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_SECRET_KEY=your_binance_secret_key_here
COINGECKO_API_KEY=your_coingecko_api_key_here
CRYPTOCOMPARE_API_KEY=your_cryptocompare_api_key_here

# Application Settings
SECRET_KEY=your-super-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Environment
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# Trading Settings
DEFAULT_RISK_PERCENTAGE=2.0
MAX_POSITIONS=5
MIN_CONFIDENCE_SCORE=0.7

# Data Collection Settings
DATA_COLLECTION_INTERVAL=60  # seconds
WEBSOCKET_RECONNECT_DELAY=5  # seconds
MAX_RECONNECT_ATTEMPTS=10
HEARTBEAT_INTERVAL=30  # seconds

# Supported Trading Pairs
SUPPORTED_SYMBOLS=BTCUSDT,ETHUSDT,BNBUSDT,ADAUSDT,SOLUSDT,MATICUSDT,DOTUSDT,AVAXUSDT,LINKUSDT,UNIUSDT

# Rate Limiting
BINANCE_RATE_LIMIT=1200  # requests per minute
COINGECKO_RATE_LIMIT=50  # requests per minute
WEBSOCKET_MAX_CONNECTIONS=5

# Data Validation
PRICE_CHANGE_THRESHOLD=0.10  # 10% max price change
DATA_STALENESS_THRESHOLD=5   # seconds
MIN_VOLUME_THRESHOLD=1000    # USDT
