"""
Demo Backtesting voi du lieu that tu Binance

Script nay se:
1. Lay du lieu lich su tu Binance API
2. Chay backtesting voi Technical Analysis strategy
3. Hien thi ket qua performance va metrics
4. Tao bao cao chi tiet
"""
import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List

from app.core.historical_data import HistoricalDataCollector
from app.core.backtesting import BacktestEngine, BacktestConfig
from app.core.signal_generator import IndicatorConfig
from app.utils.logger import setup_logging, get_logger

# Thiet lap logging
setup_logging("INFO")
logger = get_logger(__name__)


class BacktestingDemo:
    """Demo class cho Backtesting System"""
    
    def __init__(self):
        """Khoi tao demo"""
        self.historical_collector = None
        self.backtest_engine = None
        
    async def run_demo(self) -> None:
        """Chay demo backtesting chinh"""
        logger.info("=== BAT DAU BACKTESTING DEMO VOI DU LIEU THAT ===")
        
        try:
            # Buoc 1: <PERSON><PERSON> hinh backtesting
            config = await self._setup_backtest_config()
            
            # Buoc 2: Lay du lieu lich su
            historical_data = await self._fetch_historical_data(config)
            
            # Buoc 3: Chay backtesting
            results = await self._run_backtesting(config, historical_data)
            
            # Buoc 4: Hien thi ket qua
            self._display_results(results)
            
            # Buoc 5: Luu ket qua
            self._save_results(results)
            
        except Exception as e:
            logger.error(f"Loi trong demo: {e}")
            raise
    
    async def _setup_backtest_config(self) -> BacktestConfig:
        """Thiet lap cau hinh backtesting"""
        logger.info("Thiet lap cau hinh backtesting...")
        
        # Thoi gian backtesting (30 ngay gan day)
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        
        # Symbols de test
        symbols = ["BTCUSDT", "ETHUSDT"]
        
        # Cau hinh indicators
        indicator_config = IndicatorConfig(
            rsi_period=14,
            rsi_overbought=70,
            rsi_oversold=30,
            ma_fast_period=9,
            ma_medium_period=21,
            ma_slow_period=50,
            macd_fast=12,
            macd_slow=26,
            macd_signal=9,
            bb_period=20,
            bb_std_multiplier=2.0,
            min_confidence=0.65,  # Chi trade signals co confidence cao
            max_signals_per_hour=3
        )
        
        config = BacktestConfig(
            start_date=start_date,
            end_date=end_date,
            symbols=symbols,
            timeframe="1h",
            initial_capital=10000.0,
            commission_rate=0.001,  # 0.1% commission
            max_position_size=0.2,  # 20% von cho moi position
            max_positions=3,  # Toi da 3 positions
            slippage_rate=0.0005,  # 0.05% slippage
            indicator_config=indicator_config
        )
        
        logger.info(f"Cau hinh: {start_date} -> {end_date}")
        logger.info(f"Symbols: {symbols}")
        logger.info(f"Von ban dau: ${config.initial_capital:,.2f}")
        logger.info(f"Timeframe: {config.timeframe}")
        
        return config
    
    async def _fetch_historical_data(self, config: BacktestConfig) -> Dict[str, List]:
        """Lay du lieu lich su tu Binance"""
        logger.info("Dang lay du lieu lich su tu Binance...")
        
        async with HistoricalDataCollector() as collector:
            # Tinh so data points se lay
            for symbol in config.symbols:
                estimated_points = collector.calculate_data_points(
                    config.timeframe, config.start_date, config.end_date
                )
                logger.info(f"{symbol}: Uoc tinh {estimated_points} data points")
            
            # Lay du lieu cho tat ca symbols
            historical_data = await collector.get_multiple_symbols_data(
                symbols=config.symbols,
                interval=config.timeframe,
                start_time=config.start_date,
                end_time=config.end_date
            )
            
            # Kiem tra du lieu
            total_candles = 0
            for symbol, data in historical_data.items():
                logger.info(f"{symbol}: {len(data)} candles")
                total_candles += len(data)
                
                if data:
                    first_time = datetime.fromtimestamp(data[0].timestamp / 1000)
                    last_time = datetime.fromtimestamp(data[-1].timestamp / 1000)
                    logger.info(f"  Tu {first_time} den {last_time}")
            
            logger.info(f"Tong cong: {total_candles} candles")
            
            if total_candles == 0:
                raise ValueError("Khong lay duoc du lieu nao tu Binance")
            
            return historical_data
    
    async def _run_backtesting(self, config: BacktestConfig, historical_data: Dict) -> Dict:
        """Chay backtesting"""
        logger.info("Bat dau chay backtesting...")
        
        # Khoi tao backtest engine
        self.backtest_engine = BacktestEngine(config)
        
        # Load du lieu
        self.backtest_engine.load_historical_data(historical_data)
        
        # Chay backtesting
        metrics = self.backtest_engine.run_backtest()
        
        # Export ket qua
        results = self.backtest_engine.export_results()
        results["metrics_object"] = metrics
        
        return results
    
    def _display_results(self, results: Dict) -> None:
        """Hien thi ket qua backtesting"""
        logger.info("\n" + "="*60)
        logger.info("KET QUA BACKTESTING")
        logger.info("="*60)
        
        metrics = results["metrics_object"]
        config = results["config"]
        
        # Performance metrics
        logger.info(f"Thoi gian: {config['start_date']} -> {config['end_date']}")
        logger.info(f"Symbols: {', '.join(config['symbols'])}")
        logger.info(f"Von ban dau: ${config['initial_capital']:,.2f}")
        logger.info(f"Von cuoi: ${metrics.final_capital:,.2f}")
        logger.info(f"Loi nhuan: ${metrics.total_return:,.2f} ({metrics.total_return_pct:.2f}%)")
        logger.info(f"Loi nhuan hang nam: {metrics.annualized_return:.2f}%")
        
        # Trading statistics
        logger.info(f"\nThong ke giao dich:")
        logger.info(f"  Tong trades: {metrics.total_trades}")
        logger.info(f"  Trades thang: {metrics.winning_trades}")
        logger.info(f"  Trades thua: {metrics.losing_trades}")
        logger.info(f"  Ti le thang: {metrics.win_rate:.1f}%")
        logger.info(f"  Loi nhuan TB/trade: ${metrics.avg_win:.2f}")
        logger.info(f"  Thua lo TB/trade: ${metrics.avg_loss:.2f}")
        logger.info(f"  Trade lon nhat: ${metrics.largest_win:.2f}")
        logger.info(f"  Thua lo lon nhat: ${metrics.largest_loss:.2f}")
        
        # Risk metrics
        logger.info(f"\nCac chi so rui ro:")
        logger.info(f"  Max Drawdown: ${metrics.max_drawdown:.2f} ({metrics.max_drawdown_pct:.2f}%)")
        logger.info(f"  Sharpe Ratio: {metrics.sharpe_ratio:.2f}")
        logger.info(f"  Sortino Ratio: {metrics.sortino_ratio:.2f}")
        logger.info(f"  Calmar Ratio: {metrics.calmar_ratio:.2f}")
        logger.info(f"  Volatility: {metrics.volatility:.2f}%")
        logger.info(f"  VaR 95%: ${metrics.var_95:.2f}")
        logger.info(f"  Max consecutive losses: {metrics.max_consecutive_losses}")
        
        # Trade analysis
        if results["trades"]:
            logger.info(f"\nPhan tich trades:")
            logger.info(f"  Thoi gian TB/trade: {metrics.avg_trade_duration:.1f} gio")
            logger.info(f"  Profit Factor: {metrics.profit_factor:.2f}")
            
            # Hien thi 5 trades dau va cuoi
            logger.info(f"\n5 Trades dau tien:")
            for i, trade in enumerate(results["trades"][:5], 1):
                logger.info(f"  {i}. {trade['side'].upper()} {trade['symbol']} "
                          f"PnL: ${trade['pnl']:.2f} ({trade['pnl_pct']:.2f}%)")
            
            if len(results["trades"]) > 5:
                logger.info(f"\n5 Trades cuoi cung:")
                for i, trade in enumerate(results["trades"][-5:], len(results["trades"])-4):
                    logger.info(f"  {i}. {trade['side'].upper()} {trade['symbol']} "
                              f"PnL: ${trade['pnl']:.2f} ({trade['pnl_pct']:.2f}%)")
        
        logger.info("="*60)
    
    def _save_results(self, results: Dict) -> None:
        """Luu ket qua vao file"""
        try:
            # Loai bo metrics_object vi khong serialize duoc
            save_results = results.copy()
            del save_results["metrics_object"]
            
            # Luu vao file JSON
            filename = f"backtest_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(save_results, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"Ket qua da duoc luu vao: {filename}")
            
        except Exception as e:
            logger.error(f"Loi khi luu ket qua: {e}")
    
    def _analyze_performance(self, results: Dict) -> None:
        """Phan tich performance chi tiet"""
        metrics = results["metrics_object"]
        
        # Performance rating
        if metrics.total_return_pct > 20:
            performance = "XUAT SAC"
        elif metrics.total_return_pct > 10:
            performance = "TOT"
        elif metrics.total_return_pct > 0:
            performance = "DUONG"
        else:
            performance = "AM"
        
        logger.info(f"\nDANH GIA PERFORMANCE: {performance}")
        
        # Risk assessment
        if metrics.max_drawdown_pct < 5:
            risk_level = "THAP"
        elif metrics.max_drawdown_pct < 15:
            risk_level = "TRUNG BINH"
        else:
            risk_level = "CAO"
        
        logger.info(f"MUC DO RUI RO: {risk_level}")
        
        # Strategy effectiveness
        if metrics.win_rate > 60 and metrics.profit_factor > 1.5:
            effectiveness = "HIEU QUA CAO"
        elif metrics.win_rate > 50 and metrics.profit_factor > 1.2:
            effectiveness = "HIEU QUA TRUNG BINH"
        else:
            effectiveness = "CAN CAI THIEN"
        
        logger.info(f"HIEU QUA CHIEN LUOC: {effectiveness}")


async def main():
    """Ham main de chay demo"""
    demo = BacktestingDemo()
    await demo.run_demo()


if __name__ == "__main__":
    print("="*70)
    print("DEMO BACKTESTING SYSTEM VOI DU LIEU THAT")
    print("="*70)
    print("Demo nay se:")
    print("✓ Lay du lieu lich su 30 ngay tu Binance API")
    print("✓ Chay backtesting voi Technical Analysis strategy")
    print("✓ Tinh toan performance metrics chi tiet")
    print("✓ Hien thi ket qua va phan tich")
    print("✓ Luu ket qua vao file JSON")
    print("="*70)
    
    # Chay demo
    asyncio.run(main())
