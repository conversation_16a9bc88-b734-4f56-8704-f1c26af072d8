"""
Demo Backtesting voi settings aggressive de tao nhieu trades hon

Script nay su dung:
- Confidence threshold thap hon (50%)
- Timeframe ngan hon (4h thay vi 1h)
- Thoi gian dai hon (60 ngay)
- Nhieu symbols hon
"""
import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List

from app.core.historical_data import HistoricalDataCollector
from app.core.backtesting import BacktestEngine, BacktestConfig
from app.core.signal_generator import IndicatorConfig
from app.utils.logger import setup_logging, get_logger

# Thiet lap logging
setup_logging("INFO")
logger = get_logger(__name__)


async def run_aggressive_backtest():
    """Chay backtesting voi settings aggressive"""
    logger.info("=== AGGRESSIVE BACKTESTING DEMO ===")
    
    # Cau hinh aggressive
    end_date = datetime.now()
    start_date = end_date - timedelta(days=60)  # 60 ngay
    
    symbols = ["BTCUSDT", "ETHUSDT", "BNBUSDT"]  # Them BNB
    
    # Indicator config voi threshold thap hon
    indicator_config = IndicatorConfig(
        rsi_period=14,
        rsi_overbought=65,  # Thap hon (65 thay vi 70)
        rsi_oversold=35,   # Cao hon (35 thay vi 30)
        ma_fast_period=9,
        ma_medium_period=21,
        ma_slow_period=50,
        macd_fast=12,
        macd_slow=26,
        macd_signal=9,
        bb_period=20,
        bb_std_multiplier=2.0,
        min_confidence=0.5,  # THAP HON: 50% thay vi 65%
        max_signals_per_hour=10  # NHIEU HON: 10 signals/hour
    )
    
    config = BacktestConfig(
        start_date=start_date,
        end_date=end_date,
        symbols=symbols,
        timeframe="4h",  # 4h timeframe de co nhieu signals hon
        initial_capital=10000.0,
        commission_rate=0.001,
        max_position_size=0.3,  # 30% von cho moi position
        max_positions=5,  # Toi da 5 positions
        slippage_rate=0.0005,
        indicator_config=indicator_config
    )
    
    logger.info(f"Cau hinh aggressive:")
    logger.info(f"  Thoi gian: {start_date} -> {end_date} (60 ngay)")
    logger.info(f"  Symbols: {symbols}")
    logger.info(f"  Timeframe: {config.timeframe}")
    logger.info(f"  Min confidence: {indicator_config.min_confidence}")
    logger.info(f"  Max position size: {config.max_position_size}")
    
    # Lay du lieu
    logger.info("Dang lay du lieu lich su...")
    async with HistoricalDataCollector() as collector:
        historical_data = await collector.get_multiple_symbols_data(
            symbols=symbols,
            interval=config.timeframe,
            start_time=start_date,
            end_time=end_date
        )
        
        # Kiem tra du lieu
        total_candles = 0
        for symbol, data in historical_data.items():
            logger.info(f"{symbol}: {len(data)} candles")
            total_candles += len(data)
        
        logger.info(f"Tong cong: {total_candles} candles")
    
    # Chay backtesting
    logger.info("Bat dau backtesting...")
    engine = BacktestEngine(config)
    engine.load_historical_data(historical_data)
    
    metrics = engine.run_backtest()
    
    # Hien thi ket qua
    logger.info("\n" + "="*60)
    logger.info("KET QUA AGGRESSIVE BACKTESTING")
    logger.info("="*60)
    
    logger.info(f"Von ban dau: ${config.initial_capital:,.2f}")
    logger.info(f"Von cuoi: ${metrics.final_capital:,.2f}")
    logger.info(f"Loi nhuan: ${metrics.total_return:,.2f} ({metrics.total_return_pct:.2f}%)")
    logger.info(f"Loi nhuan hang nam: {metrics.annualized_return:.2f}%")
    
    logger.info(f"\nThong ke giao dich:")
    logger.info(f"  Tong trades: {metrics.total_trades}")
    logger.info(f"  Trades thang: {metrics.winning_trades}")
    logger.info(f"  Trades thua: {metrics.losing_trades}")
    logger.info(f"  Ti le thang: {metrics.win_rate:.1f}%")
    
    if metrics.total_trades > 0:
        logger.info(f"  Loi nhuan TB/trade: ${metrics.avg_win:.2f}")
        logger.info(f"  Thua lo TB/trade: ${metrics.avg_loss:.2f}")
        logger.info(f"  Trade lon nhat: ${metrics.largest_win:.2f}")
        logger.info(f"  Thua lo lon nhat: ${metrics.largest_loss:.2f}")
        logger.info(f"  Profit Factor: {metrics.profit_factor:.2f}")
    
    logger.info(f"\nRui ro:")
    logger.info(f"  Max Drawdown: ${metrics.max_drawdown:.2f} ({metrics.max_drawdown_pct:.2f}%)")
    logger.info(f"  Sharpe Ratio: {metrics.sharpe_ratio:.2f}")
    logger.info(f"  Volatility: {metrics.volatility:.2f}%")
    
    # Hien thi trades neu co
    if engine.trades:
        logger.info(f"\nChi tiet trades:")
        for i, trade in enumerate(engine.trades[:10], 1):  # Hien thi 10 trades dau
            logger.info(f"  {i}. {trade['side'].upper()} {trade['symbol']} "
                      f"Entry: ${trade['entry_price']:.2f} Exit: ${trade['exit_price']:.2f} "
                      f"PnL: ${trade['pnl']:.2f} ({trade['pnl_pct']:.2f}%)")
    
    # Luu ket qua
    results = engine.export_results()
    filename = f"aggressive_backtest_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False, default=str)
    
    logger.info(f"\nKet qua da luu vao: {filename}")
    logger.info("="*60)


async def run_comparison_test():
    """Chay test so sanh giua conservative va aggressive"""
    logger.info("=== SO SANH CONSERVATIVE VS AGGRESSIVE ===")
    
    # Test 1: Conservative (settings ban dau)
    logger.info("\n1. CONSERVATIVE STRATEGY:")
    
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)
    
    conservative_config = IndicatorConfig(
        min_confidence=0.7,  # Cao
        max_signals_per_hour=3,  # It
        rsi_overbought=70,
        rsi_oversold=30
    )
    
    config1 = BacktestConfig(
        start_date=start_date,
        end_date=end_date,
        symbols=["BTCUSDT", "ETHUSDT"],
        timeframe="1h",
        initial_capital=10000.0,
        commission_rate=0.001,
        max_position_size=0.1,  # 10%
        max_positions=2,
        indicator_config=conservative_config
    )
    
    # Test 2: Aggressive
    logger.info("\n2. AGGRESSIVE STRATEGY:")
    
    aggressive_config = IndicatorConfig(
        min_confidence=0.5,  # Thap
        max_signals_per_hour=10,  # Nhieu
        rsi_overbought=65,
        rsi_oversold=35
    )
    
    config2 = BacktestConfig(
        start_date=start_date,
        end_date=end_date,
        symbols=["BTCUSDT", "ETHUSDT"],
        timeframe="1h",
        initial_capital=10000.0,
        commission_rate=0.001,
        max_position_size=0.3,  # 30%
        max_positions=5,
        indicator_config=aggressive_config
    )
    
    # Lay du lieu chung
    async with HistoricalDataCollector() as collector:
        historical_data = await collector.get_multiple_symbols_data(
            symbols=["BTCUSDT", "ETHUSDT"],
            interval="1h",
            start_time=start_date,
            end_time=end_date
        )
    
    # Test conservative
    logger.info("Chay Conservative strategy...")
    engine1 = BacktestEngine(config1)
    engine1.load_historical_data(historical_data)
    metrics1 = engine1.run_backtest()
    
    # Test aggressive
    logger.info("Chay Aggressive strategy...")
    engine2 = BacktestEngine(config2)
    engine2.load_historical_data(historical_data)
    metrics2 = engine2.run_backtest()
    
    # So sanh ket qua
    logger.info("\n" + "="*60)
    logger.info("SO SANH KET QUA")
    logger.info("="*60)
    
    logger.info(f"{'Metric':<25} {'Conservative':<15} {'Aggressive':<15}")
    logger.info("-" * 55)
    logger.info(f"{'Total Trades':<25} {metrics1.total_trades:<15} {metrics2.total_trades:<15}")
    logger.info(f"{'Win Rate (%)':<25} {metrics1.win_rate:<15.1f} {metrics2.win_rate:<15.1f}")
    logger.info(f"{'Total Return (%)':<25} {metrics1.total_return_pct:<15.2f} {metrics2.total_return_pct:<15.2f}")
    logger.info(f"{'Max Drawdown (%)':<25} {metrics1.max_drawdown_pct:<15.2f} {metrics2.max_drawdown_pct:<15.2f}")
    logger.info(f"{'Sharpe Ratio':<25} {metrics1.sharpe_ratio:<15.2f} {metrics2.sharpe_ratio:<15.2f}")
    
    # Ket luan
    if metrics2.total_trades > metrics1.total_trades:
        logger.info(f"\n✅ Aggressive strategy tao nhieu trades hon ({metrics2.total_trades} vs {metrics1.total_trades})")
    
    if metrics2.total_return_pct > metrics1.total_return_pct:
        logger.info(f"✅ Aggressive strategy co loi nhuan cao hon ({metrics2.total_return_pct:.2f}% vs {metrics1.total_return_pct:.2f}%)")
    else:
        logger.info(f"⚠️  Conservative strategy an toan hon ({metrics1.total_return_pct:.2f}% vs {metrics2.total_return_pct:.2f}%)")


async def main():
    """Ham main"""
    print("="*70)
    print("AGGRESSIVE BACKTESTING DEMO")
    print("="*70)
    print("Demo nay se:")
    print("✓ Su dung settings aggressive de tao nhieu trades")
    print("✓ Test voi 60 ngay du lieu va 3 symbols")
    print("✓ So sanh Conservative vs Aggressive strategies")
    print("="*70)
    
    # Chon demo
    choice = input("\nChon demo (1=Aggressive, 2=Comparison, 3=Both): ").strip()
    
    if choice in ["1", "3"]:
        await run_aggressive_backtest()
    
    if choice in ["2", "3"]:
        await run_comparison_test()


if __name__ == "__main__":
    asyncio.run(main())
