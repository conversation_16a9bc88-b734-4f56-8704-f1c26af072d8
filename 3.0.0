Collecting pycoingecko
  Downloading pycoingecko-3.2.0-py3-none-any.whl.metadata (16 kB)
Requirement already satisfied: requests in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from pycoingecko) (2.32.2)
Requirement already satisfied: charset-normalizer<4,>=2 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from requests->pycoingecko) (3.3.2)
Requirement already satisfied: idna<4,>=2.5 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from requests->pycoingecko) (3.7)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from requests->pycoingecko) (2.2.1)
Requirement already satisfied: certifi>=2017.4.17 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from requests->pycoingecko) (2024.2.2)
Downloading pycoingecko-3.2.0-py3-none-any.whl (10 kB)
Installing collected packages: pycoingecko
Successfully installed pycoingecko-3.2.0
