"""
Demo script để test kết nối với các API cryptocurrency
Kiểm tra Binance WebSocket và CoinGecko API
"""
import asyncio
import json
from datetime import datetime
from app.core.binance_client import BinanceWebSocketClient, BinanceRESTClient
from app.core.coingecko_client import CoinGeckoClient
from app.config.settings import settings
from app.utils.logger import setup_logging, get_logger

# Thiết lập logging
setup_logging("INFO")
logger = get_logger(__name__)


async def test_binance_websocket():
    """Test kết nối Binance WebSocket"""
    logger.info("=== KIỂM TRA BINANCE WEBSOCKET ===")
    
    # Tạo client
    ws_client = BinanceWebSocketClient()
    
    # Danh sách symbols để test
    test_symbols = ["BTCUSDT", "ETHUSDT"]
    
    # Callback function để xử lý dữ liệu
    async def handle_data(stream_name: str, data: dict):
        """Xử lý dữ liệu nhận được từ WebSocket"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        if stream_name == "ticker":
            symbol = data.get("s", "")
            price = data.get("c", "0")
            change_percent = data.get("P", "0")
            logger.info(f"[{timestamp}] TICKER - {symbol}: ${price} ({change_percent}%)")
            
        elif stream_name == "kline":
            kline = data.get("k", {})
            symbol = kline.get("s", "")
            timeframe = kline.get("i", "")
            close_price = kline.get("c", "0")
            volume = kline.get("v", "0")
            logger.info(f"[{timestamp}] KLINE - {symbol} {timeframe}: ${close_price} (Vol: {volume})")
    
    try:
        # Kết nối ticker stream
        success = await ws_client.connect("ticker", test_symbols, handle_data)
        if success:
            logger.info("✅ Kết nối Binance WebSocket ticker thành công!")
            
            # Lắng nghe dữ liệu trong 30 giây
            logger.info("Đang lắng nghe dữ liệu trong 30 giây...")
            listen_task = asyncio.create_task(ws_client.listen("ticker"))
            
            # Chờ 30 giây
            await asyncio.sleep(30)
            
            # Dừng kết nối
            await ws_client.disconnect("ticker")
            logger.info("✅ Đã ngắt kết nối Binance WebSocket")
        else:
            logger.error("❌ Không thể kết nối Binance WebSocket")
            
    except Exception as e:
        logger.error(f"❌ Lỗi khi test Binance WebSocket: {e}")


async def test_binance_rest_api():
    """Test kết nối Binance REST API"""
    logger.info("\n=== KIỂM TRA BINANCE REST API ===")
    
    try:
        async with BinanceRESTClient() as client:
            # Test lấy thông tin exchange
            logger.info("Đang lấy thông tin exchange...")
            exchange_info = await client.get_exchange_info()
            
            if exchange_info:
                symbols_count = len(exchange_info.get("symbols", []))
                logger.info(f"✅ Binance REST API hoạt động! Có {symbols_count} trading pairs")
            
            # Test lấy ticker 24h
            logger.info("Đang lấy ticker 24h cho BTC/USDT...")
            ticker = await client.get_24hr_ticker("BTCUSDT")
            
            if ticker:
                price = ticker.get("lastPrice", "0")
                change = ticker.get("priceChangePercent", "0")
                volume = ticker.get("volume", "0")
                logger.info(f"✅ BTC/USDT: ${price} ({change}%) - Volume: {volume}")
            
            # Test lấy dữ liệu klines
            logger.info("Đang lấy dữ liệu klines cho ETH/USDT...")
            klines = await client.get_klines("ETHUSDT", "1h", 5)
            
            if klines:
                logger.info(f"✅ Nhận được {len(klines)} klines cho ETH/USDT")
                # Hiển thị kline mới nhất
                latest = klines[-1]
                open_price = latest[1]
                close_price = latest[4]
                logger.info(f"   Kline mới nhất: Open=${open_price}, Close=${close_price}")
                
    except Exception as e:
        logger.error(f"❌ Lỗi khi test Binance REST API: {e}")


async def test_coingecko_api():
    """Test kết nối CoinGecko API"""
    logger.info("\n=== KIỂM TRA COINGECKO API ===")
    
    try:
        async with CoinGeckoClient() as client:
            # Test ping
            logger.info("Đang ping CoinGecko API...")
            ping_result = await client.ping()
            
            if ping_result.get("gecko_says") == "(V3) To the Moon!":
                logger.info("✅ CoinGecko API ping thành công!")
            
            # Test lấy giá đơn giản
            logger.info("Đang lấy giá từ CoinGecko...")
            test_symbols = ["BTCUSDT", "ETHUSDT", "BNBUSDT"]
            prices = await client.get_simple_price(test_symbols)
            
            if prices:
                logger.info("✅ Dữ liệu giá từ CoinGecko:")
                for symbol, data in prices.items():
                    price = data.get("usd", 0)
                    change_24h = data.get("usd_24h_change", 0)
                    logger.info(f"   {symbol}: ${price} ({change_24h:+.2f}%)")
            
            # Test lấy dữ liệu thị trường
            logger.info("Đang lấy dữ liệu thị trường...")
            market_data = await client.get_market_data(["BTCUSDT", "ETHUSDT"])
            
            if market_data:
                logger.info(f"✅ Nhận được dữ liệu thị trường cho {len(market_data)} coins")
                for coin in market_data[:2]:  # Hiển thị 2 coin đầu
                    name = coin.get("name", "")
                    price = coin.get("current_price", 0)
                    market_cap = coin.get("market_cap", 0)
                    logger.info(f"   {name}: ${price} - Market Cap: ${market_cap:,.0f}")
                    
    except Exception as e:
        logger.error(f"❌ Lỗi khi test CoinGecko API: {e}")


async def test_rate_limiting():
    """Test rate limiting mechanisms"""
    logger.info("\n=== KIỂM TRA RATE LIMITING ===")
    
    try:
        async with CoinGeckoClient() as client:
            logger.info("Đang test rate limiting với 5 requests liên tiếp...")
            
            for i in range(5):
                start_time = asyncio.get_event_loop().time()
                
                # Gửi request
                await client.ping()
                
                end_time = asyncio.get_event_loop().time()
                duration = end_time - start_time
                
                logger.info(f"   Request {i+1}: {duration:.2f}s")
                
            logger.info("✅ Rate limiting hoạt động bình thường")
            
    except Exception as e:
        logger.error(f"❌ Lỗi khi test rate limiting: {e}")


async def main():
    """Hàm chính để chạy tất cả các test"""
    logger.info("🚀 BẮt ĐẦU KIỂM TRA KẾT NỐI API CRYPTOCURRENCY")
    logger.info(f"Môi trường: {settings.ENVIRONMENT}")
    logger.info(f"Symbols hỗ trợ: {settings.supported_symbols_list}")
    
    try:
        # Test từng API một cách tuần tự
        await test_binance_rest_api()
        await test_coingecko_api()
        await test_rate_limiting()
        
        # Test WebSocket cuối cùng (sẽ chạy trong 30 giây)
        await test_binance_websocket()
        
        logger.info("\n🎉 HOÀN THÀNH TẤT CẢ CÁC KIỂM TRA!")
        logger.info("Hệ thống sẵn sàng để thu thập dữ liệu real-time.")
        
    except KeyboardInterrupt:
        logger.info("\n⏹️  Người dùng dừng chương trình")
    except Exception as e:
        logger.error(f"\n💥 Lỗi không mong muốn: {e}")


if __name__ == "__main__":
    """Chạy demo khi file được execute trực tiếp"""
    print("=" * 60)
    print("🔗 DEMO KIỂM TRA KẾT NỐI API CRYPTOCURRENCY")
    print("=" * 60)
    print("Script này sẽ kiểm tra:")
    print("✓ Binance REST API")
    print("✓ Binance WebSocket")
    print("✓ CoinGecko API")
    print("✓ Rate Limiting")
    print("=" * 60)
    
    # Chạy async main function
    asyncio.run(main())
