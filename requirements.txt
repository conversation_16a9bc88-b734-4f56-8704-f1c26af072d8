# Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Database
sqlalchemy==2.0.23
psycopg2-binary==2.9.9
alembic==1.12.1

# Cache
redis==5.0.1
hiredis==2.2.3

# Data Processing
pandas==2.1.3
numpy==1.25.2
TA-Lib==0.4.28

# HTTP Requests
httpx==0.25.2
aiohttp==3.9.1

# Cryptocurrency APIs
python-binance==1.0.19
pycoingecko==3.1.0

# Configuration
pydantic==2.5.0
pydantic-settings==2.1.0
python-dotenv==1.0.0

# Logging
loguru==0.7.2

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1

# Development
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# Scheduling
apscheduler==3.10.4

# Math & Statistics
scipy==1.11.4
scikit-learn==1.3.2

# Plotting (for backtesting visualization)
matplotlib==3.8.2
plotly==5.17.0

# JWT Authentication
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# WebSocket
websockets==12.0
