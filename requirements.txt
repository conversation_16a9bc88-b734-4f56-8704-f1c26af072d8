# Core dependencies for Technical Analysis Demo
# Cài đặt từng bước để tránh conflicts

# Data Processing (cài đầu tiên)
numpy>=1.24.0
pandas>=2.0.0

# HTTP và WebSocket
aiohttp>=3.8.0
websockets>=11.0

# Configuration
pydantic>=2.0.0
pydantic-settings>=2.0.0
python-dotenv>=1.0.0

# Cryptocurrency APIs
pycoingecko>=3.0.0

# Optional: Advanced features (cài sau nếu cần)
# fastapi>=0.100.0
# uvicorn[standard]>=0.20.0
# redis>=5.0.0
# sqlalchemy>=2.0.0
# TA-Lib>=0.4.25
# scipy>=1.10.0
# matplotlib>=3.7.0
