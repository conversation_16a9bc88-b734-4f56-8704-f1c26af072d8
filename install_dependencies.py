"""
Script cài đặt dependencies từng bước để tránh conflicts
Đặc biệt tối ưu cho Python 3.12 trên Windows
"""
import subprocess
import sys
import os


def run_command(command, description):
    """Chạy command và hiển thị kết quả"""
    print(f"\n{'='*60}")
    print(f"🔧 {description}")
    print(f"Command: {command}")
    print('='*60)
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        print("✅ Thành công!")
        if result.stdout:
            print(f"Output: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Lỗi: {e}")
        if e.stdout:
            print(f"Stdout: {e.stdout}")
        if e.stderr:
            print(f"Stderr: {e.stderr}")
        return False


def check_python_version():
    """Kiểm tra Python version"""
    version = sys.version_info
    print(f"🐍 Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major != 3 or version.minor < 8:
        print("❌ Cần Python 3.8 trở lên!")
        return False
    
    if version.minor >= 12:
        print("⚠️  Python 3.12 detected - sẽ cài đặt packages tương thích")
    
    return True


def upgrade_pip():
    """Upgrade pip to latest version"""
    return run_command(
        f"{sys.executable} -m pip install --upgrade pip",
        "Upgrade pip to latest version"
    )


def install_core_packages():
    """Cài đặt core packages trước"""
    packages = [
        "setuptools>=65.0.0",
        "wheel>=0.38.0",
        "packaging>=21.0"
    ]
    
    for package in packages:
        success = run_command(
            f"{sys.executable} -m pip install {package}",
            f"Cài đặt {package}"
        )
        if not success:
            return False
    
    return True


def install_data_packages():
    """Cài đặt data processing packages"""
    packages = [
        "numpy>=1.24.0",
        "pandas>=2.0.0"
    ]
    
    for package in packages:
        success = run_command(
            f"{sys.executable} -m pip install {package}",
            f"Cài đặt {package}"
        )
        if not success:
            print(f"⚠️  Không thể cài {package}, thử version khác...")
            # Fallback to older versions
            if "numpy" in package:
                success = run_command(
                    f"{sys.executable} -m pip install numpy",
                    "Cài đặt numpy (latest compatible)"
                )
            elif "pandas" in package:
                success = run_command(
                    f"{sys.executable} -m pip install pandas",
                    "Cài đặt pandas (latest compatible)"
                )
            
            if not success:
                return False
    
    return True


def install_network_packages():
    """Cài đặt networking packages"""
    packages = [
        "aiohttp>=3.8.0",
        "websockets>=11.0",
        "httpx>=0.24.0"
    ]
    
    for package in packages:
        success = run_command(
            f"{sys.executable} -m pip install {package}",
            f"Cài đặt {package}"
        )
        if not success:
            return False
    
    return True


def install_config_packages():
    """Cài đặt configuration packages"""
    packages = [
        "pydantic>=2.0.0",
        "pydantic-settings>=2.0.0",
        "python-dotenv>=1.0.0"
    ]
    
    for package in packages:
        success = run_command(
            f"{sys.executable} -m pip install {package}",
            f"Cài đặt {package}"
        )
        if not success:
            return False
    
    return True


def install_crypto_packages():
    """Cài đặt cryptocurrency API packages"""
    packages = [
        "pycoingecko>=3.0.0"
    ]
    
    for package in packages:
        success = run_command(
            f"{sys.executable} -m pip install {package}",
            f"Cài đặt {package}"
        )
        if not success:
            print(f"⚠️  Không thể cài {package}, thử cài manual...")
            success = run_command(
                f"{sys.executable} -m pip install pycoingecko",
                "Cài đặt pycoingecko (latest)"
            )
            if not success:
                return False
    
    return True


def verify_installation():
    """Kiểm tra installation"""
    print(f"\n{'='*60}")
    print("🔍 KIỂM TRA CÀI ĐẶT")
    print('='*60)
    
    required_packages = [
        "numpy",
        "pandas", 
        "aiohttp",
        "websockets",
        "pydantic",
        "pydantic_settings",
        "dotenv",
        "pycoingecko"
    ]
    
    failed_packages = []
    
    for package in required_packages:
        try:
            if package == "dotenv":
                import python_dotenv
                print(f"✅ {package}: {python_dotenv.__version__}")
            elif package == "pydantic_settings":
                import pydantic_settings
                print(f"✅ {package}: {pydantic_settings.__version__}")
            else:
                module = __import__(package)
                version = getattr(module, '__version__', 'unknown')
                print(f"✅ {package}: {version}")
        except ImportError:
            print(f"❌ {package}: NOT INSTALLED")
            failed_packages.append(package)
    
    if failed_packages:
        print(f"\n⚠️  Các packages chưa cài đặt: {failed_packages}")
        return False
    else:
        print(f"\n🎉 TẤT CẢ PACKAGES ĐÃ CÀI ĐẶT THÀNH CÔNG!")
        return True


def main():
    """Main installation process"""
    print("🚀 BẮT ĐẦU CÀI ĐẶT DEPENDENCIES CHO CRYPTO TRADING BOT")
    print("Tối ưu cho Python 3.12 trên Windows")
    
    # Kiểm tra Python version
    if not check_python_version():
        return False
    
    # Upgrade pip
    if not upgrade_pip():
        print("⚠️  Không thể upgrade pip, tiếp tục...")
    
    # Cài đặt từng nhóm packages
    steps = [
        (install_core_packages, "Core packages (setuptools, wheel)"),
        (install_data_packages, "Data processing packages (numpy, pandas)"),
        (install_network_packages, "Network packages (aiohttp, websockets)"),
        (install_config_packages, "Configuration packages (pydantic)"),
        (install_crypto_packages, "Cryptocurrency packages (pycoingecko)")
    ]
    
    for step_func, step_name in steps:
        print(f"\n📦 {step_name}")
        if not step_func():
            print(f"❌ Lỗi trong bước: {step_name}")
            print("💡 Thử chạy lại script hoặc cài đặt manual")
            return False
    
    # Verify installation
    if verify_installation():
        print(f"\n🎉 CÀI ĐẶT HOÀN TẤT!")
        print("Bây giờ có thể chạy demo:")
        print("python test_technical_analysis_demo.py")
        return True
    else:
        print(f"\n❌ CÀI ĐẶT CHƯA HOÀN CHỈNH")
        return False


if __name__ == "__main__":
    success = main()
    if not success:
        print(f"\n💡 HƯỚNG DẪN KHẮC PHỤC:")
        print("1. Thử chạy lại script: python install_dependencies.py")
        print("2. Hoặc cài đặt manual từng package:")
        print("   pip install numpy pandas aiohttp websockets pydantic pydantic-settings python-dotenv pycoingecko")
        print("3. Nếu vẫn lỗi, thử tạo virtual environment mới:")
        print("   python -m venv new_env")
        print("   new_env\\Scripts\\activate")
        print("   python install_dependencies.py")
    
    input("\nNhấn Enter để thoát...")
