"""
Debug script de kiem tra tai sao khong co signals duoc tao

Script nay se:
1. Lay du lieu that tu Binance
2. <PERSON><PERSON> tra tung buoc cua signal generation
3. <PERSON><PERSON> thi chi tiet indicators va logic
4. <PERSON> ra tai sao khong co trades
"""
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List

from app.core.historical_data import HistoricalDataCollector
from app.core.signal_generator import SignalGenerator, IndicatorConfig
from app.core.data_collector import MarketData
from app.utils.logger import setup_logging, get_logger

# Thiet lap logging
setup_logging("DEBUG")  # DEBUG level de xem chi tiet
logger = get_logger(__name__)


async def debug_signal_generation():
    """Debug signal generation process"""
    logger.info("=== DEBUG SIGNAL GENERATION ===")
    
    # Lay du lieu ngan han de debug
    end_date = datetime.now()
    start_date = end_date - timedelta(days=7)  # Chi 7 ngay de debug nhanh
    
    # Cau hinh rat thap de de tao signals
    indicator_config = IndicatorConfig(
        rsi_period=14,
        rsi_overbought=60,  # RAT THAP
        rsi_oversold=40,   # RAT CAO
        ma_fast_period=9,
        ma_medium_period=21,
        ma_slow_period=50,
        macd_fast=12,
        macd_slow=26,
        macd_signal=9,
        bb_period=20,
        bb_std_multiplier=2.0,
        min_confidence=0.3,  # RAT THAP: 30%
        max_signals_per_hour=20  # RAT NHIEU
    )
    
    logger.info(f"Cau hinh debug:")
    logger.info(f"  RSI overbought: {indicator_config.rsi_overbought}")
    logger.info(f"  RSI oversold: {indicator_config.rsi_oversold}")
    logger.info(f"  Min confidence: {indicator_config.min_confidence}")
    
    # Lay du lieu
    logger.info("Lay du lieu 7 ngay gan day...")
    async with HistoricalDataCollector() as collector:
        historical_data = await collector.get_multiple_symbols_data(
            symbols=["BTCUSDT"],  # Chi test 1 symbol
            interval="1h",
            start_time=start_date,
            end_time=end_date
        )
    
    if not historical_data["BTCUSDT"]:
        logger.error("Khong co du lieu!")
        return
    
    data = historical_data["BTCUSDT"]
    logger.info(f"Co {len(data)} candles cho BTCUSDT")
    
    # Khoi tao signal generator
    signal_gen = SignalGenerator(indicator_config)
    
    # Process tung candle va kiem tra
    signals_generated = []
    
    for i, market_data in enumerate(data):
        # Process market data
        signal = signal_gen.process_market_data(market_data)
        
        if signal:
            signals_generated.append(signal)
            logger.info(f"SIGNAL #{len(signals_generated)}: {signal.signal_type.value} "
                       f"at ${signal.price:.2f} (confidence: {signal.confidence:.2f})")
        
        # Hien thi indicators moi 50 candles
        if i % 50 == 0:
            indicators = signal_gen.get_latest_indicators()
            if indicators:
                logger.info(f"Candle {i}: Price=${market_data.close:.2f}")
                if indicators.rsi:
                    logger.info(f"  RSI: {indicators.rsi:.1f}")
                if indicators.ema_9 and indicators.ema_21:
                    logger.info(f"  EMA 9: ${indicators.ema_9:.2f}, EMA 21: ${indicators.ema_21:.2f}")
                if indicators.macd_line and indicators.macd_signal:
                    logger.info(f"  MACD: {indicators.macd_line:.4f}, Signal: {indicators.macd_signal:.4f}")
    
    logger.info(f"\nTong cong tao duoc {len(signals_generated)} signals")
    
    if not signals_generated:
        logger.warning("KHONG CO SIGNALS NAO DUOC TAO!")
        
        # Kiem tra indicators cuoi cung
        final_indicators = signal_gen.get_latest_indicators()
        if final_indicators:
            logger.info("Indicators cuoi cung:")
            logger.info(f"  Price: ${final_indicators.price:.2f}")
            logger.info(f"  RSI: {final_indicators.rsi}")
            logger.info(f"  EMA 9: {final_indicators.ema_9}")
            logger.info(f"  EMA 21: {final_indicators.ema_21}")
            logger.info(f"  MACD Line: {final_indicators.macd_line}")
            logger.info(f"  MACD Signal: {final_indicators.macd_signal}")
            
            # Kiem tra dieu kien signal
            if final_indicators.rsi:
                if final_indicators.rsi < indicator_config.rsi_oversold:
                    logger.info(f"  -> RSI oversold: {final_indicators.rsi} < {indicator_config.rsi_oversold}")
                elif final_indicators.rsi > indicator_config.rsi_overbought:
                    logger.info(f"  -> RSI overbought: {final_indicators.rsi} > {indicator_config.rsi_overbought}")
                else:
                    logger.info(f"  -> RSI neutral: {final_indicators.rsi}")
        
        # Kiem tra _has_sufficient_data
        logger.info("Kiem tra _has_sufficient_data...")
        if final_indicators:
            required_indicators = [
                final_indicators.rsi,
                final_indicators.ema_9,
                final_indicators.ema_21,
                final_indicators.macd_line,
                final_indicators.macd_signal
            ]
            
            for i, (name, value) in enumerate([
                ("RSI", final_indicators.rsi),
                ("EMA 9", final_indicators.ema_9),
                ("EMA 21", final_indicators.ema_21),
                ("MACD Line", final_indicators.macd_line),
                ("MACD Signal", final_indicators.macd_signal)
            ]):
                if value is None:
                    logger.warning(f"  {name}: MISSING!")
                else:
                    logger.info(f"  {name}: OK ({value})")
            
            has_sufficient = all(ind is not None for ind in required_indicators)
            logger.info(f"Has sufficient data: {has_sufficient}")
    
    else:
        logger.info("Signals duoc tao:")
        for i, signal in enumerate(signals_generated, 1):
            logger.info(f"  {i}. {signal.signal_type.value} at ${signal.price:.2f} "
                       f"(confidence: {signal.confidence:.2f})")
            logger.info(f"     Reasoning: {signal.reasoning}")


async def test_manual_signal_creation():
    """Test tao signal thu cong"""
    logger.info("\n=== TEST MANUAL SIGNAL CREATION ===")
    
    # Tao mock data voi gia tri extreme
    mock_data = MarketData(
        symbol="BTCUSDT",
        timestamp=int(datetime.now().timestamp() * 1000),
        source="test",
        close=50000.0,
        volume=1000.0,
        timeframe="1h"
    )
    
    # Cau hinh rat thap
    config = IndicatorConfig(
        rsi_period=5,  # Period ngan
        rsi_overbought=55,  # Rat thap
        rsi_oversold=45,   # Rat cao
        ma_fast_period=3,
        ma_medium_period=5,
        ma_slow_period=10,
        min_confidence=0.1  # Rat thap
    )
    
    signal_gen = SignalGenerator(config)
    
    # Tao du lieu trending
    prices = [
        48000, 47000, 46000, 45000, 44000,  # Downtrend
        43000, 42000, 41000, 40000, 39000,  # Continue down
        38000, 39000, 40000, 41000, 42000,  # Recovery
        43000, 44000, 45000, 46000, 47000,  # Uptrend
        48000, 49000, 50000, 51000, 52000   # Continue up
    ]
    
    logger.info("Feeding trending price data...")
    signals = []
    
    for i, price in enumerate(prices):
        mock_data.close = price
        mock_data.timestamp = int((datetime.now() + timedelta(hours=i)).timestamp() * 1000)
        
        signal = signal_gen.process_market_data(mock_data)
        
        if signal:
            signals.append(signal)
            logger.info(f"SIGNAL: {signal.signal_type.value} at ${price} "
                       f"(confidence: {signal.confidence:.2f})")
        
        # Hien thi indicators
        indicators = signal_gen.get_latest_indicators()
        if indicators and i % 5 == 0:
            logger.info(f"Step {i}: Price=${price}")
            if indicators.rsi:
                logger.info(f"  RSI: {indicators.rsi:.1f}")
            if indicators.ema_9 and indicators.ema_21:
                trend = "UP" if indicators.ema_9 > indicators.ema_21 else "DOWN"
                logger.info(f"  Trend: {trend} (EMA9: ${indicators.ema_9:.0f}, EMA21: ${indicators.ema_21:.0f})")
    
    logger.info(f"\nTong signals tao duoc: {len(signals)}")
    
    if signals:
        for signal in signals:
            logger.info(f"  {signal.signal_type.value}: ${signal.price:.0f} "
                       f"({signal.confidence:.1%}) - {signal.reasoning}")
    else:
        logger.warning("Van khong co signals nao!")


async def main():
    """Ham main"""
    print("="*60)
    print("DEBUG SIGNAL GENERATION")
    print("="*60)
    print("Script nay se debug tai sao khong co signals")
    print("="*60)
    
    await debug_signal_generation()
    await test_manual_signal_creation()


if __name__ == "__main__":
    asyncio.run(main())
