"""
Backtesting Framework - <PERSON><PERSON> thống backtesting cho crypto trading strategies

Module này cung cấp framework để test trading strategies trên dữ liệu lịch sử
với performance analytics và risk management metrics
"""
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass, field
from enum import Enum
import json

from app.core.technical_analysis import TechnicalSignal, SignalType
from app.core.signal_generator import SignalGenerator, IndicatorConfig
from app.core.data_collector import MarketData
from app.utils.logger import LoggerMixin


class OrderType(Enum):
    """Loại lệnh trading"""
    MARKET = "market"
    LIMIT = "limit"
    STOP_LOSS = "stop_loss"
    TAKE_PROFIT = "take_profit"


class OrderStatus(Enum):
    """Trạng thái lệnh"""
    PENDING = "pending"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"


@dataclass
class Order:
    """Lệnh trading trong backtesting"""
    id: str
    symbol: str
    side: str  # "buy" hoặc "sell"
    order_type: OrderType
    quantity: float
    price: float
    timestamp: int
    status: OrderStatus = OrderStatus.PENDING
    filled_price: Optional[float] = None
    filled_quantity: float = 0.0
    filled_timestamp: Optional[int] = None
    commission: float = 0.0
    signal_id: Optional[str] = None  # Link tới signal tạo ra order này


@dataclass
class Position:
    """Vị thế trading"""
    symbol: str
    side: str  # "long" hoặc "short"
    quantity: float
    entry_price: float
    entry_timestamp: int
    current_price: float = 0.0
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    orders: List[Order] = field(default_factory=list)


@dataclass
class BacktestConfig:
    """Cấu hình cho backtesting"""
    # Thời gian backtesting
    start_date: datetime
    end_date: datetime
    
    # Symbols để test
    symbols: List[str]
    
    # Timeframe data
    timeframe: str = "1h"
    
    # Vốn ban đầu
    initial_capital: float = 10000.0
    
    # Commission (phí giao dịch)
    commission_rate: float = 0.001  # 0.1%
    
    # Risk management
    max_position_size: float = 0.1  # 10% vốn cho 1 position
    max_positions: int = 5  # Tối đa 5 positions cùng lúc
    
    # Slippage (trượt giá)
    slippage_rate: float = 0.0005  # 0.05%
    
    # Indicator configuration
    indicator_config: IndicatorConfig = field(default_factory=IndicatorConfig)


@dataclass
class BacktestMetrics:
    """Metrics kết quả backtesting"""
    # Thời gian
    start_date: datetime
    end_date: datetime
    duration_days: int
    
    # Performance
    initial_capital: float
    final_capital: float
    total_return: float
    total_return_pct: float
    annualized_return: float
    
    # Trading stats
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    
    # Risk metrics
    max_drawdown: float
    max_drawdown_pct: float
    sharpe_ratio: float
    sortino_ratio: float
    calmar_ratio: float
    
    # Trade analysis
    avg_win: float
    avg_loss: float
    profit_factor: float
    largest_win: float
    largest_loss: float
    
    # Additional metrics
    volatility: float
    var_95: float  # Value at Risk 95%
    max_consecutive_losses: int
    avg_trade_duration: float  # hours


class BacktestEngine(LoggerMixin):
    """
    Backtesting Engine chính
    
    Thực hiện backtesting trading strategies trên dữ liệu lịch sử
    với simulation trading environment hoàn chỉnh
    """
    
    def __init__(self, config: BacktestConfig):
        """
        Khởi tạo Backtest Engine
        
        Args:
            config: Cấu hình backtesting
        """
        self.config = config
        
        # Trading state
        self.current_capital = config.initial_capital
        self.positions: Dict[str, Position] = {}
        self.orders: List[Order] = []
        self.trades: List[Dict[str, Any]] = []
        
        # Performance tracking
        self.equity_curve: List[Tuple[int, float]] = []
        self.daily_returns: List[float] = []
        
        # Signal generator
        self.signal_generator = SignalGenerator(config.indicator_config)
        
        # Data storage
        self.historical_data: Dict[str, List[MarketData]] = {}
        
        # Counters
        self.order_id_counter = 1
        self.trade_id_counter = 1
        
        self.logger.info(f"Backtest Engine khởi tạo: {config.start_date} -> {config.end_date}")
        self.logger.info(f"Symbols: {config.symbols}, Initial Capital: ${config.initial_capital:,.2f}")
    
    def load_historical_data(self, data: Dict[str, List[MarketData]]) -> None:
        """
        Load dữ liệu lịch sử cho backtesting
        
        Args:
            data: Dictionary chứa historical data cho mỗi symbol
        """
        self.historical_data = data
        
        total_candles = sum(len(symbol_data) for symbol_data in data.values())
        self.logger.info(f"Đã load {total_candles} candles cho {len(data)} symbols")
        
        # Validate data
        for symbol, symbol_data in data.items():
            if not symbol_data:
                raise ValueError(f"Không có dữ liệu cho symbol {symbol}")
            
            # Sort by timestamp
            symbol_data.sort(key=lambda x: x.timestamp)
            
            start_time = datetime.fromtimestamp(symbol_data[0].timestamp / 1000)
            end_time = datetime.fromtimestamp(symbol_data[-1].timestamp / 1000)
            
            self.logger.info(f"{symbol}: {len(symbol_data)} candles từ {start_time} đến {end_time}")
    
    def run_backtest(self) -> BacktestMetrics:
        """
        Chạy backtesting chính
        
        Returns:
            BacktestMetrics với kết quả chi tiết
        """
        self.logger.info("🚀 Bắt đầu backtesting...")
        start_time = time.time()
        
        # Reset state
        self._reset_state()
        
        # Tạo timeline từ tất cả data
        timeline = self._create_timeline()
        
        if not timeline:
            raise ValueError("Không có dữ liệu để backtesting")
        
        self.logger.info(f"Timeline: {len(timeline)} data points")
        
        # Process từng timestamp
        for i, (timestamp, market_data_dict) in enumerate(timeline):
            self._process_timestamp(timestamp, market_data_dict)
            
            # Log progress
            if i % 1000 == 0:
                progress = (i / len(timeline)) * 100
                self.logger.info(f"Progress: {progress:.1f}% - Capital: ${self.current_capital:,.2f}")
        
        # Đóng tất cả positions
        self._close_all_positions(timeline[-1][0])
        
        # Tính toán metrics
        metrics = self._calculate_metrics()
        
        duration = time.time() - start_time
        self.logger.info(f"✅ Backtesting hoàn thành trong {duration:.2f}s")
        self.logger.info(f"Final Capital: ${metrics.final_capital:,.2f}")
        self.logger.info(f"Total Return: {metrics.total_return_pct:.2f}%")
        self.logger.info(f"Win Rate: {metrics.win_rate:.1f}%")
        
        return metrics
    
    def _reset_state(self) -> None:
        """Reset trạng thái backtesting"""
        self.current_capital = self.config.initial_capital
        self.positions.clear()
        self.orders.clear()
        self.trades.clear()
        self.equity_curve.clear()
        self.daily_returns.clear()
        self.order_id_counter = 1
        self.trade_id_counter = 1
        
        # Reset signal generator
        self.signal_generator.reset()
    
    def _create_timeline(self) -> List[Tuple[int, Dict[str, MarketData]]]:
        """
        Tạo timeline từ tất cả historical data
        
        Returns:
            List các (timestamp, {symbol: MarketData}) được sort theo thời gian
        """
        timeline_dict: Dict[int, Dict[str, MarketData]] = {}
        
        # Collect tất cả timestamps
        for symbol, data_list in self.historical_data.items():
            for market_data in data_list:
                timestamp = market_data.timestamp
                
                if timestamp not in timeline_dict:
                    timeline_dict[timestamp] = {}
                
                timeline_dict[timestamp][symbol] = market_data
        
        # Convert to sorted list
        timeline = [(ts, data_dict) for ts, data_dict in sorted(timeline_dict.items())]
        
        # Filter theo date range
        start_ts = int(self.config.start_date.timestamp() * 1000)
        end_ts = int(self.config.end_date.timestamp() * 1000)
        
        timeline = [(ts, data_dict) for ts, data_dict in timeline 
                   if start_ts <= ts <= end_ts]
        
        return timeline
    
    def _process_timestamp(self, timestamp: int, market_data_dict: Dict[str, MarketData]) -> None:
        """
        Xử lý một timestamp trong backtesting
        
        Args:
            timestamp: Timestamp hiện tại
            market_data_dict: Market data cho tất cả symbols tại timestamp này
        """
        # Update current prices
        current_prices = {}
        for symbol, market_data in market_data_dict.items():
            current_prices[symbol] = market_data.close or market_data.price or 0.0
        
        # Process pending orders
        self._process_orders(timestamp, current_prices)
        
        # Update positions
        self._update_positions(current_prices)
        
        # Generate signals
        for symbol, market_data in market_data_dict.items():
            signal = self.signal_generator.process_market_data(market_data)
            if signal:
                self._process_signal(signal, timestamp, current_prices[symbol])
        
        # Update equity curve
        total_equity = self._calculate_total_equity(current_prices)
        self.equity_curve.append((timestamp, total_equity))
        
        # Calculate daily returns
        if len(self.equity_curve) > 1:
            prev_equity = self.equity_curve[-2][1]
            daily_return = (total_equity - prev_equity) / prev_equity
            self.daily_returns.append(daily_return)
