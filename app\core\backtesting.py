"""
Backtesting Framework - <PERSON><PERSON> thống backtesting cho crypto trading strategies

Module này cung cấp framework để test trading strategies trên dữ liệu lịch sử
với performance analytics và risk management metrics
"""
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass, field
from enum import Enum
import json

from app.core.technical_analysis import TechnicalSignal, SignalType
from app.core.signal_generator import SignalGenerator, IndicatorConfig
from app.core.data_collector import MarketData
from app.utils.logger import LoggerMixin


class OrderType(Enum):
    """Loại lệnh trading"""
    MARKET = "market"
    LIMIT = "limit"
    STOP_LOSS = "stop_loss"
    TAKE_PROFIT = "take_profit"


class OrderStatus(Enum):
    """Trạng thái lệnh"""
    PENDING = "pending"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"


@dataclass
class Order:
    """Lệnh trading trong backtesting"""
    id: str
    symbol: str
    side: str  # "buy" hoặc "sell"
    order_type: OrderType
    quantity: float
    price: float
    timestamp: int
    status: OrderStatus = OrderStatus.PENDING
    filled_price: Optional[float] = None
    filled_quantity: float = 0.0
    filled_timestamp: Optional[int] = None
    commission: float = 0.0
    signal_id: Optional[str] = None  # Link tới signal tạo ra order này


@dataclass
class Position:
    """Vị thế trading"""
    symbol: str
    side: str  # "long" hoặc "short"
    quantity: float
    entry_price: float
    entry_timestamp: int
    current_price: float = 0.0
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    orders: List[Order] = field(default_factory=list)


@dataclass
class BacktestConfig:
    """Cấu hình cho backtesting"""
    # Thời gian backtesting
    start_date: datetime
    end_date: datetime
    
    # Symbols để test
    symbols: List[str]
    
    # Timeframe data
    timeframe: str = "1h"
    
    # Vốn ban đầu
    initial_capital: float = 10000.0
    
    # Commission (phí giao dịch)
    commission_rate: float = 0.001  # 0.1%
    
    # Risk management
    max_position_size: float = 0.1  # 10% vốn cho 1 position
    max_positions: int = 5  # Tối đa 5 positions cùng lúc
    
    # Slippage (trượt giá)
    slippage_rate: float = 0.0005  # 0.05%
    
    # Indicator configuration
    indicator_config: IndicatorConfig = field(default_factory=IndicatorConfig)


@dataclass
class BacktestMetrics:
    """Metrics kết quả backtesting"""
    # Thời gian
    start_date: datetime
    end_date: datetime
    duration_days: int
    
    # Performance
    initial_capital: float
    final_capital: float
    total_return: float
    total_return_pct: float
    annualized_return: float
    
    # Trading stats
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    
    # Risk metrics
    max_drawdown: float
    max_drawdown_pct: float
    sharpe_ratio: float
    sortino_ratio: float
    calmar_ratio: float
    
    # Trade analysis
    avg_win: float
    avg_loss: float
    profit_factor: float
    largest_win: float
    largest_loss: float
    
    # Additional metrics
    volatility: float
    var_95: float  # Value at Risk 95%
    max_consecutive_losses: int
    avg_trade_duration: float  # hours


class BacktestEngine(LoggerMixin):
    """
    Backtesting Engine chính
    
    Thực hiện backtesting trading strategies trên dữ liệu lịch sử
    với simulation trading environment hoàn chỉnh
    """
    
    def __init__(self, config: BacktestConfig):
        """
        Khởi tạo Backtest Engine
        
        Args:
            config: Cấu hình backtesting
        """
        self.config = config
        
        # Trading state
        self.current_capital = config.initial_capital
        self.positions: Dict[str, Position] = {}
        self.orders: List[Order] = []
        self.trades: List[Dict[str, Any]] = []
        
        # Performance tracking
        self.equity_curve: List[Tuple[int, float]] = []
        self.daily_returns: List[float] = []
        
        # Signal generator
        self.signal_generator = SignalGenerator(config.indicator_config)
        
        # Data storage
        self.historical_data: Dict[str, List[MarketData]] = {}
        
        # Counters
        self.order_id_counter = 1
        self.trade_id_counter = 1
        
        self.logger.info(f"Backtest Engine khởi tạo: {config.start_date} -> {config.end_date}")
        self.logger.info(f"Symbols: {config.symbols}, Initial Capital: ${config.initial_capital:,.2f}")
    
    def load_historical_data(self, data: Dict[str, List[MarketData]]) -> None:
        """
        Load dữ liệu lịch sử cho backtesting
        
        Args:
            data: Dictionary chứa historical data cho mỗi symbol
        """
        self.historical_data = data
        
        total_candles = sum(len(symbol_data) for symbol_data in data.values())
        self.logger.info(f"Đã load {total_candles} candles cho {len(data)} symbols")
        
        # Validate data
        for symbol, symbol_data in data.items():
            if not symbol_data:
                raise ValueError(f"Không có dữ liệu cho symbol {symbol}")
            
            # Sort by timestamp
            symbol_data.sort(key=lambda x: x.timestamp)
            
            start_time = datetime.fromtimestamp(symbol_data[0].timestamp / 1000)
            end_time = datetime.fromtimestamp(symbol_data[-1].timestamp / 1000)
            
            self.logger.info(f"{symbol}: {len(symbol_data)} candles từ {start_time} đến {end_time}")
    
    def run_backtest(self) -> BacktestMetrics:
        """
        Chạy backtesting chính
        
        Returns:
            BacktestMetrics với kết quả chi tiết
        """
        self.logger.info("🚀 Bắt đầu backtesting...")
        start_time = time.time()
        
        # Reset state
        self._reset_state()
        
        # Tạo timeline từ tất cả data
        timeline = self._create_timeline()
        
        if not timeline:
            raise ValueError("Không có dữ liệu để backtesting")
        
        self.logger.info(f"Timeline: {len(timeline)} data points")
        
        # Process từng timestamp
        for i, (timestamp, market_data_dict) in enumerate(timeline):
            self._process_timestamp(timestamp, market_data_dict)
            
            # Log progress
            if i % 1000 == 0:
                progress = (i / len(timeline)) * 100
                self.logger.info(f"Progress: {progress:.1f}% - Capital: ${self.current_capital:,.2f}")
        
        # Đóng tất cả positions
        self._close_all_positions(timeline[-1][0])
        
        # Tính toán metrics
        metrics = self._calculate_metrics()
        
        duration = time.time() - start_time
        self.logger.info(f"✅ Backtesting hoàn thành trong {duration:.2f}s")
        self.logger.info(f"Final Capital: ${metrics.final_capital:,.2f}")
        self.logger.info(f"Total Return: {metrics.total_return_pct:.2f}%")
        self.logger.info(f"Win Rate: {metrics.win_rate:.1f}%")
        
        return metrics
    
    def _reset_state(self) -> None:
        """Reset trạng thái backtesting"""
        self.current_capital = self.config.initial_capital
        self.positions.clear()
        self.orders.clear()
        self.trades.clear()
        self.equity_curve.clear()
        self.daily_returns.clear()
        self.order_id_counter = 1
        self.trade_id_counter = 1
        
        # Reset signal generator
        self.signal_generator.reset()
    
    def _create_timeline(self) -> List[Tuple[int, Dict[str, MarketData]]]:
        """
        Tạo timeline từ tất cả historical data
        
        Returns:
            List các (timestamp, {symbol: MarketData}) được sort theo thời gian
        """
        timeline_dict: Dict[int, Dict[str, MarketData]] = {}
        
        # Collect tất cả timestamps
        for symbol, data_list in self.historical_data.items():
            for market_data in data_list:
                timestamp = market_data.timestamp
                
                if timestamp not in timeline_dict:
                    timeline_dict[timestamp] = {}
                
                timeline_dict[timestamp][symbol] = market_data
        
        # Convert to sorted list
        timeline = [(ts, data_dict) for ts, data_dict in sorted(timeline_dict.items())]
        
        # Filter theo date range
        start_ts = int(self.config.start_date.timestamp() * 1000)
        end_ts = int(self.config.end_date.timestamp() * 1000)
        
        timeline = [(ts, data_dict) for ts, data_dict in timeline 
                   if start_ts <= ts <= end_ts]
        
        return timeline

    def _process_timestamp(self, timestamp: int, market_data_dict: Dict[str, MarketData]) -> None:
        """
        Xu ly mot timestamp trong backtesting

        Args:
            timestamp: Timestamp hien tai
            market_data_dict: Market data cho tat ca symbols tai timestamp nay
        """
        # Update current prices
        current_prices = {}
        for symbol, market_data in market_data_dict.items():
            current_prices[symbol] = market_data.close or market_data.price or 0.0

        # Process pending orders
        self._process_orders(timestamp, current_prices)

        # Update positions
        self._update_positions(current_prices)

        # Generate signals
        for symbol, market_data in market_data_dict.items():
            signal = self.signal_generator.process_market_data(market_data)
            if signal:
                self._process_signal(signal, timestamp, current_prices[symbol])

        # Update equity curve
        total_equity = self._calculate_total_equity(current_prices)
        self.equity_curve.append((timestamp, total_equity))

        # Calculate daily returns
        if len(self.equity_curve) > 1:
            prev_equity = self.equity_curve[-2][1]
            daily_return = (total_equity - prev_equity) / prev_equity
            self.daily_returns.append(daily_return)

    def _process_orders(self, timestamp: int, current_prices: Dict[str, float]) -> None:
        """Xu ly cac orders pending"""
        for order in self.orders[:]:  # Copy list de co the modify
            if order.status != OrderStatus.PENDING:
                continue

            symbol_price = current_prices.get(order.symbol, 0.0)
            if symbol_price == 0.0:
                continue

            # Kiem tra xem order co duoc fill khong
            filled = False
            fill_price = symbol_price

            if order.order_type == OrderType.MARKET:
                # Market order luon duoc fill
                filled = True
                # Apply slippage
                if order.side == "buy":
                    fill_price = symbol_price * (1 + self.config.slippage_rate)
                else:
                    fill_price = symbol_price * (1 - self.config.slippage_rate)

            elif order.order_type == OrderType.LIMIT:
                # Limit order chi fill khi price dat target
                if order.side == "buy" and symbol_price <= order.price:
                    filled = True
                    fill_price = order.price
                elif order.side == "sell" and symbol_price >= order.price:
                    filled = True
                    fill_price = order.price

            elif order.order_type == OrderType.STOP_LOSS:
                # Stop loss order
                if order.side == "sell" and symbol_price <= order.price:
                    filled = True
                    fill_price = symbol_price * (1 - self.config.slippage_rate)
                elif order.side == "buy" and symbol_price >= order.price:
                    filled = True
                    fill_price = symbol_price * (1 + self.config.slippage_rate)

            elif order.order_type == OrderType.TAKE_PROFIT:
                # Take profit order
                if order.side == "sell" and symbol_price >= order.price:
                    filled = True
                    fill_price = symbol_price * (1 - self.config.slippage_rate)
                elif order.side == "buy" and symbol_price <= order.price:
                    filled = True
                    fill_price = symbol_price * (1 + self.config.slippage_rate)

            if filled:
                self._fill_order(order, fill_price, timestamp)

    def _fill_order(self, order: Order, fill_price: float, timestamp: int) -> None:
        """Fill mot order"""
        order.status = OrderStatus.FILLED
        order.filled_price = fill_price
        order.filled_quantity = order.quantity
        order.filled_timestamp = timestamp

        # Tinh commission
        order.commission = order.quantity * fill_price * self.config.commission_rate

        # Update capital
        if order.side == "buy":
            cost = order.quantity * fill_price + order.commission
            self.current_capital -= cost
        else:
            proceeds = order.quantity * fill_price - order.commission
            self.current_capital += proceeds

        # Update hoac tao position
        self._update_position_from_order(order)

        self.logger.info(f"Order filled: {order.side} {order.quantity} {order.symbol} "
                        f"at ${fill_price:.4f} (Commission: ${order.commission:.2f})")

    def _update_position_from_order(self, order: Order) -> None:
        """Update position tu order da fill"""
        symbol = order.symbol

        if symbol not in self.positions:
            # Tao position moi
            if order.side == "buy":
                self.positions[symbol] = Position(
                    symbol=symbol,
                    side="long",
                    quantity=order.quantity,
                    entry_price=order.filled_price,
                    entry_timestamp=order.filled_timestamp
                )
            else:
                self.positions[symbol] = Position(
                    symbol=symbol,
                    side="short",
                    quantity=order.quantity,
                    entry_price=order.filled_price,
                    entry_timestamp=order.filled_timestamp
                )
        else:
            # Update position hien tai
            position = self.positions[symbol]

            if (position.side == "long" and order.side == "sell") or \
               (position.side == "short" and order.side == "buy"):
                # Dong position (hoan toan hoac mot phan)
                if order.quantity >= position.quantity:
                    # Dong hoan toan position
                    realized_pnl = self._calculate_realized_pnl(position, order.filled_price, position.quantity)
                    self._record_trade(position, order.filled_price, order.filled_timestamp, realized_pnl)
                    del self.positions[symbol]
                else:
                    # Dong mot phan position
                    realized_pnl = self._calculate_realized_pnl(position, order.filled_price, order.quantity)
                    position.quantity -= order.quantity
                    position.realized_pnl += realized_pnl
            else:
                # Tang position (cung huong)
                total_cost = position.quantity * position.entry_price + order.quantity * order.filled_price
                position.quantity += order.quantity
                position.entry_price = total_cost / position.quantity

        # Them order vao position
        if symbol in self.positions:
            self.positions[symbol].orders.append(order)

    def _calculate_realized_pnl(self, position: Position, exit_price: float, quantity: float) -> float:
        """Tinh realized PnL khi dong position"""
        if position.side == "long":
            return (exit_price - position.entry_price) * quantity
        else:
            return (position.entry_price - exit_price) * quantity

    def _record_trade(self, position: Position, exit_price: float, exit_timestamp: int, pnl: float) -> None:
        """Ghi lai trade da hoan thanh"""
        trade = {
            "id": self.trade_id_counter,
            "symbol": position.symbol,
            "side": position.side,
            "quantity": position.quantity,
            "entry_price": position.entry_price,
            "exit_price": exit_price,
            "entry_timestamp": position.entry_timestamp,
            "exit_timestamp": exit_timestamp,
            "duration_hours": (exit_timestamp - position.entry_timestamp) / (1000 * 3600),
            "pnl": pnl,
            "pnl_pct": (pnl / (position.quantity * position.entry_price)) * 100,
            "commission": sum(order.commission for order in position.orders)
        }

        self.trades.append(trade)
        self.trade_id_counter += 1

        self.logger.info(f"Trade completed: {trade['side']} {trade['symbol']} "
                        f"PnL: ${pnl:.2f} ({trade['pnl_pct']:.2f}%)")

    def _update_positions(self, current_prices: Dict[str, float]) -> None:
        """Update tat ca positions voi gia hien tai"""
        for symbol, position in self.positions.items():
            if symbol in current_prices:
                position.current_price = current_prices[symbol]
                position.unrealized_pnl = self._calculate_unrealized_pnl(position)

    def _calculate_unrealized_pnl(self, position: Position) -> float:
        """Tinh unrealized PnL cua position"""
        if position.side == "long":
            return (position.current_price - position.entry_price) * position.quantity
        else:
            return (position.entry_price - position.current_price) * position.quantity

    def _process_signal(self, signal: TechnicalSignal, timestamp: int, current_price: float) -> None:
        """Xu ly signal moi"""
        # Kiem tra xem co nen tao order khong
        if not self._should_create_order(signal):
            return

        # Tinh position size
        position_size = self._calculate_position_size(signal, current_price)
        if position_size <= 0:
            return

        # Tao order
        if signal.signal_type in [SignalType.BUY, SignalType.STRONG_BUY]:
            order = self._create_buy_order(signal, position_size, current_price, timestamp)
        elif signal.signal_type in [SignalType.SELL, SignalType.STRONG_SELL]:
            order = self._create_sell_order(signal, position_size, current_price, timestamp)
        else:
            return  # HOLD signal

        if order:
            self.orders.append(order)
            self.logger.info(f"Order created: {order.side} {order.quantity} {order.symbol} at ${order.price:.4f}")

    def _should_create_order(self, signal: TechnicalSignal) -> bool:
        """Kiem tra xem co nen tao order tu signal khong"""
        # Kiem tra confidence threshold
        if signal.confidence < self.config.indicator_config.min_confidence:
            return False

        # Kiem tra max positions
        if len(self.positions) >= self.config.max_positions:
            return False

        # Kiem tra xem da co position cho symbol nay chua
        if signal.symbol in self.positions:
            position = self.positions[signal.symbol]

            # Neu signal cung huong voi position hien tai, khong tao order moi
            if (position.side == "long" and signal.signal_type in [SignalType.BUY, SignalType.STRONG_BUY]) or \
               (position.side == "short" and signal.signal_type in [SignalType.SELL, SignalType.STRONG_SELL]):
                return False

        return True

    def _calculate_position_size(self, signal: TechnicalSignal, current_price: float) -> float:
        """Tinh position size cho order"""
        # Tinh max capital cho position nay
        max_capital = self.current_capital * self.config.max_position_size

        # Tinh quantity dua tren max capital
        quantity = max_capital / current_price

        # Adjust dua tren confidence
        confidence_multiplier = signal.confidence
        quantity *= confidence_multiplier

        return quantity

    def _create_buy_order(self, signal: TechnicalSignal, quantity: float,
                         current_price: float, timestamp: int) -> Optional[Order]:
        """Tao buy order"""
        order = Order(
            id=str(self.order_id_counter),
            symbol=signal.symbol,
            side="buy",
            order_type=OrderType.MARKET,
            quantity=quantity,
            price=current_price,
            timestamp=timestamp,
            signal_id=f"{signal.symbol}_{timestamp}"
        )

        self.order_id_counter += 1
        return order

    def _create_sell_order(self, signal: TechnicalSignal, quantity: float,
                          current_price: float, timestamp: int) -> Optional[Order]:
        """Tao sell order"""
        # Kiem tra xem co position de sell khong
        if signal.symbol not in self.positions:
            return None

        position = self.positions[signal.symbol]
        if position.side != "long":
            return None

        # Sell toan bo hoac mot phan position
        sell_quantity = min(quantity, position.quantity)

        order = Order(
            id=str(self.order_id_counter),
            symbol=signal.symbol,
            side="sell",
            order_type=OrderType.MARKET,
            quantity=sell_quantity,
            price=current_price,
            timestamp=timestamp,
            signal_id=f"{signal.symbol}_{timestamp}"
        )

        self.order_id_counter += 1
        return order

    def _calculate_total_equity(self, current_prices: Dict[str, float]) -> float:
        """Tinh tong equity (capital + unrealized PnL)"""
        total_equity = self.current_capital

        for position in self.positions.values():
            if position.symbol in current_prices:
                position.current_price = current_prices[position.symbol]
                unrealized_pnl = self._calculate_unrealized_pnl(position)
                total_equity += unrealized_pnl

        return total_equity

    def _close_all_positions(self, timestamp: int) -> None:
        """Dong tat ca positions con lai"""
        for symbol, position in list(self.positions.items()):
            # Tao market order de dong position
            if position.side == "long":
                order = Order(
                    id=str(self.order_id_counter),
                    symbol=symbol,
                    side="sell",
                    order_type=OrderType.MARKET,
                    quantity=position.quantity,
                    price=position.current_price,
                    timestamp=timestamp
                )
            else:
                order = Order(
                    id=str(self.order_id_counter),
                    symbol=symbol,
                    side="buy",
                    order_type=OrderType.MARKET,
                    quantity=position.quantity,
                    price=position.current_price,
                    timestamp=timestamp
                )

            self.order_id_counter += 1
            self._fill_order(order, position.current_price, timestamp)

    def _calculate_metrics(self) -> BacktestMetrics:
        """Tinh toan tat ca metrics"""
        if not self.trades:
            # Khong co trades nao
            return BacktestMetrics(
                start_date=self.config.start_date,
                end_date=self.config.end_date,
                duration_days=(self.config.end_date - self.config.start_date).days,
                initial_capital=self.config.initial_capital,
                final_capital=self.current_capital,
                total_return=self.current_capital - self.config.initial_capital,
                total_return_pct=((self.current_capital - self.config.initial_capital) / self.config.initial_capital) * 100,
                annualized_return=0.0,
                total_trades=0,
                winning_trades=0,
                losing_trades=0,
                win_rate=0.0,
                max_drawdown=0.0,
                max_drawdown_pct=0.0,
                sharpe_ratio=0.0,
                sortino_ratio=0.0,
                calmar_ratio=0.0,
                avg_win=0.0,
                avg_loss=0.0,
                profit_factor=0.0,
                largest_win=0.0,
                largest_loss=0.0,
                volatility=0.0,
                var_95=0.0,
                max_consecutive_losses=0,
                avg_trade_duration=0.0
            )

        # Basic metrics
        duration_days = (self.config.end_date - self.config.start_date).days
        total_return = self.current_capital - self.config.initial_capital
        total_return_pct = (total_return / self.config.initial_capital) * 100

        # Annualized return
        years = duration_days / 365.25
        annualized_return = ((self.current_capital / self.config.initial_capital) ** (1/years) - 1) * 100 if years > 0 else 0

        # Trade statistics
        winning_trades = len([t for t in self.trades if t['pnl'] > 0])
        losing_trades = len([t for t in self.trades if t['pnl'] < 0])
        win_rate = (winning_trades / len(self.trades)) * 100 if self.trades else 0

        # PnL analysis
        wins = [t['pnl'] for t in self.trades if t['pnl'] > 0]
        losses = [t['pnl'] for t in self.trades if t['pnl'] < 0]

        avg_win = sum(wins) / len(wins) if wins else 0
        avg_loss = sum(losses) / len(losses) if losses else 0
        largest_win = max(wins) if wins else 0
        largest_loss = min(losses) if losses else 0

        # Profit factor
        total_wins = sum(wins) if wins else 0
        total_losses = abs(sum(losses)) if losses else 0
        profit_factor = total_wins / total_losses if total_losses > 0 else 0

        # Risk metrics
        max_drawdown, max_drawdown_pct = self._calculate_drawdown()
        sharpe_ratio = self._calculate_sharpe_ratio()
        sortino_ratio = self._calculate_sortino_ratio()
        calmar_ratio = annualized_return / abs(max_drawdown_pct) if max_drawdown_pct != 0 else 0

        # Additional metrics
        volatility = self._calculate_volatility()
        var_95 = self._calculate_var_95()
        max_consecutive_losses = self._calculate_max_consecutive_losses()
        avg_trade_duration = sum(t['duration_hours'] for t in self.trades) / len(self.trades) if self.trades else 0

        return BacktestMetrics(
            start_date=self.config.start_date,
            end_date=self.config.end_date,
            duration_days=duration_days,
            initial_capital=self.config.initial_capital,
            final_capital=self.current_capital,
            total_return=total_return,
            total_return_pct=total_return_pct,
            annualized_return=annualized_return,
            total_trades=len(self.trades),
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            win_rate=win_rate,
            max_drawdown=max_drawdown,
            max_drawdown_pct=max_drawdown_pct,
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=sortino_ratio,
            calmar_ratio=calmar_ratio,
            avg_win=avg_win,
            avg_loss=avg_loss,
            profit_factor=profit_factor,
            largest_win=largest_win,
            largest_loss=largest_loss,
            volatility=volatility,
            var_95=var_95,
            max_consecutive_losses=max_consecutive_losses,
            avg_trade_duration=avg_trade_duration
        )

    def _calculate_drawdown(self) -> Tuple[float, float]:
        """Tinh max drawdown"""
        if not self.equity_curve:
            return 0.0, 0.0

        peak = self.config.initial_capital
        max_drawdown = 0.0
        max_drawdown_pct = 0.0

        for timestamp, equity in self.equity_curve:
            if equity > peak:
                peak = equity

            drawdown = peak - equity
            drawdown_pct = (drawdown / peak) * 100 if peak > 0 else 0

            if drawdown > max_drawdown:
                max_drawdown = drawdown
                max_drawdown_pct = drawdown_pct

        return max_drawdown, max_drawdown_pct

    def _calculate_sharpe_ratio(self) -> float:
        """Tinh Sharpe ratio"""
        if not self.daily_returns:
            return 0.0

        import statistics

        avg_return = statistics.mean(self.daily_returns)
        std_return = statistics.stdev(self.daily_returns) if len(self.daily_returns) > 1 else 0

        if std_return == 0:
            return 0.0

        # Annualize (gia su 365 trading days)
        sharpe = (avg_return * 365) / (std_return * (365 ** 0.5))
        return sharpe

    def _calculate_sortino_ratio(self) -> float:
        """Tinh Sortino ratio"""
        if not self.daily_returns:
            return 0.0

        import statistics

        avg_return = statistics.mean(self.daily_returns)
        negative_returns = [r for r in self.daily_returns if r < 0]

        if not negative_returns:
            return float('inf') if avg_return > 0 else 0.0

        downside_std = statistics.stdev(negative_returns) if len(negative_returns) > 1 else 0

        if downside_std == 0:
            return 0.0

        # Annualize
        sortino = (avg_return * 365) / (downside_std * (365 ** 0.5))
        return sortino

    def _calculate_volatility(self) -> float:
        """Tinh volatility (annualized)"""
        if not self.daily_returns:
            return 0.0

        import statistics

        std_return = statistics.stdev(self.daily_returns) if len(self.daily_returns) > 1 else 0
        return std_return * (365 ** 0.5) * 100  # Annualized percentage

    def _calculate_var_95(self) -> float:
        """Tinh Value at Risk 95%"""
        if not self.daily_returns:
            return 0.0

        sorted_returns = sorted(self.daily_returns)
        index = int(0.05 * len(sorted_returns))  # 5th percentile

        if index < len(sorted_returns):
            var_95 = sorted_returns[index] * self.current_capital
            return var_95

        return 0.0

    def _calculate_max_consecutive_losses(self) -> int:
        """Tinh so losses lien tiep toi da"""
        if not self.trades:
            return 0

        max_consecutive = 0
        current_consecutive = 0

        for trade in self.trades:
            if trade['pnl'] < 0:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0

        return max_consecutive

    def get_trade_summary(self) -> Dict[str, Any]:
        """Lay tom tat trades"""
        if not self.trades:
            return {"message": "Khong co trades nao"}

        return {
            "total_trades": len(self.trades),
            "profitable_trades": len([t for t in self.trades if t['pnl'] > 0]),
            "losing_trades": len([t for t in self.trades if t['pnl'] < 0]),
            "total_pnl": sum(t['pnl'] for t in self.trades),
            "avg_pnl": sum(t['pnl'] for t in self.trades) / len(self.trades),
            "best_trade": max(self.trades, key=lambda x: x['pnl']),
            "worst_trade": min(self.trades, key=lambda x: x['pnl']),
            "avg_duration_hours": sum(t['duration_hours'] for t in self.trades) / len(self.trades)
        }

    def export_results(self) -> Dict[str, Any]:
        """Export ket qua backtesting"""
        metrics = self._calculate_metrics()

        return {
            "config": {
                "start_date": self.config.start_date.isoformat(),
                "end_date": self.config.end_date.isoformat(),
                "symbols": self.config.symbols,
                "initial_capital": self.config.initial_capital,
                "commission_rate": self.config.commission_rate,
                "max_position_size": self.config.max_position_size
            },
            "metrics": {
                "final_capital": metrics.final_capital,
                "total_return_pct": metrics.total_return_pct,
                "annualized_return": metrics.annualized_return,
                "win_rate": metrics.win_rate,
                "sharpe_ratio": metrics.sharpe_ratio,
                "max_drawdown_pct": metrics.max_drawdown_pct,
                "total_trades": metrics.total_trades
            },
            "trades": self.trades,
            "equity_curve": self.equity_curve
        }
    
    def _process_timestamp(self, timestamp: int, market_data_dict: Dict[str, MarketData]) -> None:
        """
        Xử lý một timestamp trong backtesting
        
        Args:
            timestamp: Timestamp hiện tại
            market_data_dict: Market data cho tất cả symbols tại timestamp này
        """
        # Update current prices
        current_prices = {}
        for symbol, market_data in market_data_dict.items():
            current_prices[symbol] = market_data.close or market_data.price or 0.0
        
        # Process pending orders
        self._process_orders(timestamp, current_prices)
        
        # Update positions
        self._update_positions(current_prices)
        
        # Generate signals
        for symbol, market_data in market_data_dict.items():
            signal = self.signal_generator.process_market_data(market_data)
            if signal:
                self._process_signal(signal, timestamp, current_prices[symbol])
        
        # Update equity curve
        total_equity = self._calculate_total_equity(current_prices)
        self.equity_curve.append((timestamp, total_equity))
        
        # Calculate daily returns
        if len(self.equity_curve) > 1:
            prev_equity = self.equity_curve[-2][1]
            daily_return = (total_equity - prev_equity) / prev_equity
            self.daily_returns.append(daily_return)
