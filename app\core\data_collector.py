"""
Main data collector that orchestrates all data sources
"""
import asyncio
import json
import time
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timezone
from dataclasses import dataclass, asdict
from enum import Enum

from app.core.binance_client import BinanceWebSocketClient, BinanceRESTClient
from app.core.coingecko_client import CoinGeckoClient, convert_coingecko_to_binance_format
from app.config.settings import settings, VALIDATION_RULES
from app.utils.logger import LoggerMixin


class DataSource(Enum):
    """Data source enumeration"""
    BINANCE_WS = "binance_websocket"
    BINANCE_REST = "binance_rest"
    COINGECKO = "coingecko"
    CACHE = "cache"


@dataclass
class MarketData:
    """Standardized market data structure"""
    symbol: str
    timestamp: int
    source: str
    
    # OHLCV data
    open: Optional[float] = None
    high: Optional[float] = None
    low: Optional[float] = None
    close: Optional[float] = None
    volume: Optional[float] = None
    
    # Ticker data
    price: Optional[float] = None
    price_change: Optional[float] = None
    price_change_percent: Optional[float] = None
    
    # Order book data
    bid_price: Optional[float] = None
    ask_price: Optional[float] = None
    bid_qty: Optional[float] = None
    ask_qty: Optional[float] = None
    
    # Additional metadata
    timeframe: Optional[str] = None
    trade_count: Optional[int] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)
    
    def is_valid(self) -> bool:
        """Validate market data"""
        # Check required fields
        if not self.symbol or not self.timestamp or not self.source:
            return False
        
        # Check timestamp freshness
        now = int(time.time() * 1000)
        if now - self.timestamp > VALIDATION_RULES["data_staleness_threshold"] * 1000:
            return False
        
        # Check price data validity
        if self.price is not None:
            if self.price <= 0:
                return False
        
        # Check OHLCV data validity
        if all(x is not None for x in [self.open, self.high, self.low, self.close]):
            if not (self.low <= self.open <= self.high and 
                   self.low <= self.close <= self.high):
                return False
        
        return True


class DataCollector(LoggerMixin):
    """Main data collector orchestrating all data sources"""
    
    def __init__(self):
        self.binance_ws = BinanceWebSocketClient()
        self.coingecko = CoinGeckoClient()
        
        # Data storage
        self.latest_data: Dict[str, MarketData] = {}
        self.data_callbacks: List[Callable] = []
        
        # Connection status
        self.connection_status: Dict[str, bool] = {
            DataSource.BINANCE_WS.value: False,
            DataSource.COINGECKO.value: False
        }
        
        # Fallback mechanism
        self.primary_source = DataSource.BINANCE_WS
        self.fallback_sources = [DataSource.COINGECKO]
        self.current_source = self.primary_source
        
        # Data quality tracking
        self.data_quality_stats = {
            "total_messages": 0,
            "valid_messages": 0,
            "invalid_messages": 0,
            "source_stats": {}
        }
    
    async def start(self, symbols: List[str]) -> None:
        """Start data collection for specified symbols"""
        self.logger.info(f"Starting data collection for symbols: {symbols}")
        
        # Initialize CoinGecko client
        async with self.coingecko as cg_client:
            # Validate CoinGecko connection
            cg_connected = await cg_client.validate_connection()
            self.connection_status[DataSource.COINGECKO.value] = cg_connected
            
            # Start primary data source (Binance WebSocket)
            try:
                await self.binance_ws.start_all_streams(symbols, self._handle_binance_data)
                self.connection_status[DataSource.BINANCE_WS.value] = True
                self.current_source = DataSource.BINANCE_WS
                self.logger.info("Primary data source (Binance WebSocket) started successfully")
                
            except Exception as e:
                self.logger.error(f"Failed to start primary data source: {e}")
                await self._switch_to_fallback(symbols)
    
    async def _handle_binance_data(self, stream_name: str, data: Dict[str, Any]) -> None:
        """Handle incoming data from Binance WebSocket"""
        try:
            # Parse different stream types
            if stream_name == "kline":
                market_data = self._parse_binance_kline(data)
            elif stream_name == "ticker":
                market_data = self._parse_binance_ticker(data)
            elif stream_name == "depth":
                market_data = self._parse_binance_depth(data)
            elif stream_name == "trade":
                market_data = self._parse_binance_trade(data)
            else:
                self.logger.warning(f"Unknown stream type: {stream_name}")
                return
            
            if market_data and market_data.is_valid():
                await self._process_market_data(market_data)
            else:
                self._update_quality_stats(False, DataSource.BINANCE_WS.value)
                
        except Exception as e:
            self.logger.error(f"Error handling Binance data: {e}")
            self._update_quality_stats(False, DataSource.BINANCE_WS.value)
    
    def _parse_binance_kline(self, data: Dict[str, Any]) -> Optional[MarketData]:
        """Parse Binance kline/candlestick data"""
        try:
            kline = data.get("k", {})
            if not kline:
                return None
            
            return MarketData(
                symbol=kline.get("s", ""),
                timestamp=kline.get("t", 0),
                source=DataSource.BINANCE_WS.value,
                timeframe=kline.get("i", ""),
                open=float(kline.get("o", 0)),
                high=float(kline.get("h", 0)),
                low=float(kline.get("l", 0)),
                close=float(kline.get("c", 0)),
                volume=float(kline.get("v", 0)),
                trade_count=int(kline.get("n", 0))
            )
            
        except (ValueError, KeyError) as e:
            self.logger.error(f"Error parsing Binance kline data: {e}")
            return None
    
    def _parse_binance_ticker(self, data: Dict[str, Any]) -> Optional[MarketData]:
        """Parse Binance 24hr ticker data"""
        try:
            return MarketData(
                symbol=data.get("s", ""),
                timestamp=data.get("E", int(time.time() * 1000)),
                source=DataSource.BINANCE_WS.value,
                price=float(data.get("c", 0)),
                price_change=float(data.get("p", 0)),
                price_change_percent=float(data.get("P", 0)),
                volume=float(data.get("v", 0))
            )
            
        except (ValueError, KeyError) as e:
            self.logger.error(f"Error parsing Binance ticker data: {e}")
            return None
    
    def _parse_binance_depth(self, data: Dict[str, Any]) -> Optional[MarketData]:
        """Parse Binance order book depth data"""
        try:
            bids = data.get("b", [])
            asks = data.get("a", [])
            
            if not bids or not asks:
                return None
            
            # Get best bid and ask
            best_bid = bids[0] if bids else ["0", "0"]
            best_ask = asks[0] if asks else ["0", "0"]
            
            return MarketData(
                symbol=data.get("s", ""),
                timestamp=data.get("E", int(time.time() * 1000)),
                source=DataSource.BINANCE_WS.value,
                bid_price=float(best_bid[0]),
                bid_qty=float(best_bid[1]),
                ask_price=float(best_ask[0]),
                ask_qty=float(best_ask[1])
            )
            
        except (ValueError, KeyError, IndexError) as e:
            self.logger.error(f"Error parsing Binance depth data: {e}")
            return None
    
    def _parse_binance_trade(self, data: Dict[str, Any]) -> Optional[MarketData]:
        """Parse Binance individual trade data"""
        try:
            return MarketData(
                symbol=data.get("s", ""),
                timestamp=data.get("T", int(time.time() * 1000)),
                source=DataSource.BINANCE_WS.value,
                price=float(data.get("p", 0)),
                volume=float(data.get("q", 0))
            )
            
        except (ValueError, KeyError) as e:
            self.logger.error(f"Error parsing Binance trade data: {e}")
            return None
    
    async def _process_market_data(self, market_data: MarketData) -> None:
        """Process and store market data"""
        # Update latest data
        key = f"{market_data.symbol}_{market_data.timeframe or 'ticker'}"
        self.latest_data[key] = market_data
        
        # Update quality stats
        self._update_quality_stats(True, market_data.source)
        
        # Call registered callbacks
        for callback in self.data_callbacks:
            try:
                await callback(market_data)
            except Exception as e:
                self.logger.error(f"Error in data callback: {e}")
        
        # Log data reception (debug level)
        self.logger.debug(f"Processed {market_data.source} data for {market_data.symbol}")
    
    def _update_quality_stats(self, is_valid: bool, source: str) -> None:
        """Update data quality statistics"""
        self.data_quality_stats["total_messages"] += 1
        
        if is_valid:
            self.data_quality_stats["valid_messages"] += 1
        else:
            self.data_quality_stats["invalid_messages"] += 1
        
        # Update source-specific stats
        if source not in self.data_quality_stats["source_stats"]:
            self.data_quality_stats["source_stats"][source] = {
                "total": 0, "valid": 0, "invalid": 0
            }
        
        stats = self.data_quality_stats["source_stats"][source]
        stats["total"] += 1
        if is_valid:
            stats["valid"] += 1
        else:
            stats["invalid"] += 1
