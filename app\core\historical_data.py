"""
Historical Data Collector - Thu thap du lieu lich su tu Binance

Module nay lay du lieu OHLCV lich su tu Binance API de phuc vu backtesting
"""
import asyncio
import aiohttp
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import time

from app.core.data_collector import MarketData
from app.utils.logger import LoggerMixin


class HistoricalDataCollector(LoggerMixin):
    """
    Collector de lay du lieu lich su tu Binance API
    
    Su dung Binance REST API de lay klines (candlestick) data
    """
    
    def __init__(self):
        """Khoi tao Historical Data Collector"""
        self.base_url = "https://api.binance.com"
        self.session: Optional[aiohttp.ClientSession] = None
        
        # Rate limiting
        self.request_count = 0
        self.last_request_time = 0
        self.max_requests_per_minute = 1200  # Binance limit
        
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def get_historical_klines(self, 
                                  symbol: str, 
                                  interval: str, 
                                  start_time: datetime, 
                                  end_time: datetime,
                                  limit: int = 1000) -> List[MarketData]:
        """
        Lay du lieu klines lich su tu Binance
        
        Args:
            symbol: Trading symbol (e.g., "BTCUSDT")
            interval: Timeframe (1m, 5m, 15m, 1h, 4h, 1d)
            start_time: Thoi gian bat dau
            end_time: Thoi gian ket thuc
            limit: So luong candles toi da moi request (max 1000)
            
        Returns:
            List MarketData objects
        """
        if not self.session:
            raise RuntimeError("Session chua duoc khoi tao. Su dung async context manager.")
        
        all_data = []
        current_start = start_time
        
        while current_start < end_time:
            # Rate limiting
            await self._check_rate_limit()
            
            # Tinh toan end time cho request nay
            if interval == "1m":
                request_end = min(current_start + timedelta(minutes=limit), end_time)
            elif interval == "5m":
                request_end = min(current_start + timedelta(minutes=limit * 5), end_time)
            elif interval == "15m":
                request_end = min(current_start + timedelta(minutes=limit * 15), end_time)
            elif interval == "1h":
                request_end = min(current_start + timedelta(hours=limit), end_time)
            elif interval == "4h":
                request_end = min(current_start + timedelta(hours=limit * 4), end_time)
            elif interval == "1d":
                request_end = min(current_start + timedelta(days=limit), end_time)
            else:
                raise ValueError(f"Interval khong ho tro: {interval}")
            
            # Tao request parameters
            params = {
                "symbol": symbol,
                "interval": interval,
                "startTime": int(current_start.timestamp() * 1000),
                "endTime": int(request_end.timestamp() * 1000),
                "limit": limit
            }
            
            try:
                # Gui request
                url = f"{self.base_url}/api/v3/klines"
                async with self.session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        # Convert sang MarketData objects
                        for kline in data:
                            market_data = self._convert_kline_to_market_data(kline, symbol, interval)
                            all_data.append(market_data)
                        
                        self.logger.info(f"Lay duoc {len(data)} candles cho {symbol} {interval} "
                                       f"tu {current_start} den {request_end}")
                        
                        # Update current_start cho request tiep theo
                        if data:
                            last_timestamp = data[-1][0]  # Open time cua candle cuoi
                            current_start = datetime.fromtimestamp(last_timestamp / 1000)
                            
                            # Them 1 interval de tranh duplicate
                            if interval == "1m":
                                current_start += timedelta(minutes=1)
                            elif interval == "5m":
                                current_start += timedelta(minutes=5)
                            elif interval == "15m":
                                current_start += timedelta(minutes=15)
                            elif interval == "1h":
                                current_start += timedelta(hours=1)
                            elif interval == "4h":
                                current_start += timedelta(hours=4)
                            elif interval == "1d":
                                current_start += timedelta(days=1)
                        else:
                            break
                    else:
                        error_text = await response.text()
                        self.logger.error(f"Loi API Binance: {response.status} - {error_text}")
                        break
                        
            except Exception as e:
                self.logger.error(f"Loi khi lay du lieu: {e}")
                break
            
            # Delay nho de tranh rate limit
            await asyncio.sleep(0.1)
        
        self.logger.info(f"Tong cong lay duoc {len(all_data)} candles cho {symbol} {interval}")
        return all_data
    
    def _convert_kline_to_market_data(self, kline: List, symbol: str, timeframe: str) -> MarketData:
        """
        Convert Binance kline data sang MarketData object
        
        Args:
            kline: Binance kline data array
            symbol: Trading symbol
            timeframe: Timeframe
            
        Returns:
            MarketData object
        """
        # Binance kline format:
        # [
        #   1499040000000,      // Open time
        #   "0.01634790",       // Open
        #   "0.80000000",       // High
        #   "0.01575800",       // Low
        #   "0.01577100",       // Close
        #   "148976.11427815",  // Volume
        #   1499644799999,      // Close time
        #   "2434.19055334",    // Quote asset volume
        #   308,                // Number of trades
        #   "1756.87402397",    // Taker buy base asset volume
        #   "28.46694368",      // Taker buy quote asset volume
        #   "17928899.62484339" // Ignore
        # ]
        
        return MarketData(
            symbol=symbol,
            timestamp=int(kline[0]),  # Open time
            source="binance_historical",
            open=float(kline[1]),
            high=float(kline[2]),
            low=float(kline[3]),
            close=float(kline[4]),
            volume=float(kline[5]),
            timeframe=timeframe,
            trade_count=int(kline[8]) if len(kline) > 8 else None
        )
    
    async def _check_rate_limit(self) -> None:
        """Kiem tra va thuc thi rate limiting"""
        current_time = time.time()
        
        # Reset counter moi phut
        if current_time - self.last_request_time > 60:
            self.request_count = 0
            self.last_request_time = current_time
        
        # Kiem tra rate limit
        if self.request_count >= self.max_requests_per_minute:
            sleep_time = 60 - (current_time - self.last_request_time)
            if sleep_time > 0:
                self.logger.warning(f"Rate limit reached. Sleeping {sleep_time:.1f}s")
                await asyncio.sleep(sleep_time)
                self.request_count = 0
                self.last_request_time = time.time()
        
        self.request_count += 1
    
    async def get_multiple_symbols_data(self, 
                                      symbols: List[str], 
                                      interval: str, 
                                      start_time: datetime, 
                                      end_time: datetime) -> Dict[str, List[MarketData]]:
        """
        Lay du lieu cho nhieu symbols cung luc
        
        Args:
            symbols: Danh sach symbols
            interval: Timeframe
            start_time: Thoi gian bat dau
            end_time: Thoi gian ket thuc
            
        Returns:
            Dictionary {symbol: List[MarketData]}
        """
        result = {}
        
        for symbol in symbols:
            self.logger.info(f"Dang lay du lieu cho {symbol}...")
            
            try:
                data = await self.get_historical_klines(symbol, interval, start_time, end_time)
                result[symbol] = data
                
                # Delay giua cac symbols
                await asyncio.sleep(0.5)
                
            except Exception as e:
                self.logger.error(f"Loi khi lay du lieu cho {symbol}: {e}")
                result[symbol] = []
        
        return result
    
    def get_available_intervals(self) -> List[str]:
        """
        Lay danh sach intervals ho tro
        
        Returns:
            List cac intervals
        """
        return ["1m", "3m", "5m", "15m", "30m", "1h", "2h", "4h", "6h", "8h", "12h", "1d", "3d", "1w", "1M"]
    
    def calculate_data_points(self, interval: str, start_time: datetime, end_time: datetime) -> int:
        """
        Tinh so data points se lay duoc
        
        Args:
            interval: Timeframe
            start_time: Thoi gian bat dau
            end_time: Thoi gian ket thuc
            
        Returns:
            So luong data points uoc tinh
        """
        duration = end_time - start_time
        
        if interval == "1m":
            return int(duration.total_seconds() / 60)
        elif interval == "5m":
            return int(duration.total_seconds() / 300)
        elif interval == "15m":
            return int(duration.total_seconds() / 900)
        elif interval == "1h":
            return int(duration.total_seconds() / 3600)
        elif interval == "4h":
            return int(duration.total_seconds() / 14400)
        elif interval == "1d":
            return int(duration.days)
        else:
            return 0
