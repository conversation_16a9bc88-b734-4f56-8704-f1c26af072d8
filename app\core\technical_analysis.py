"""
Technical Analysis Module - <PERSON><PERSON> tích kỹ thuật cho crypto trading bot

<PERSON><PERSON><PERSON> này chứa các technical indicators chính:
- RSI (Relative Strength Index)
- MACD (Moving Average Convergence Divergence)
- Moving Averages (SMA, EMA)
- Bollinger Bands
- Volume Indicators

Tất cả indicators được thiết kế để xử lý dữ liệu real-time và multiple timeframes
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum

from app.utils.logger import LoggerMixin


class SignalType(Enum):
    """Enum định nghĩa các loại tín hiệu trading"""
    BUY = "BUY"           # Tín hiệu mua (Long)
    SELL = "SELL"         # Tín hiệu bán (Short)
    HOLD = "HOLD"         # Giữ nguyên position
    STRONG_BUY = "STRONG_BUY"     # Tín hiệu mua mạnh
    STRONG_SELL = "STRONG_SELL"   # Tín hiệu bán mạnh


@dataclass
class TechnicalSignal:
    """
    Cấu trúc dữ liệu cho technical signal
    Chứa thông tin về tín hiệu trading được tạo từ technical analysis
    """
    symbol: str                    # Trading symbol (ví dụ: BTCUSDT)
    timeframe: str                 # Khung thời gian (1m, 5m, 1h, etc.)
    signal_type: SignalType        # Loại tín hiệu (BUY/SELL/HOLD)
    confidence: float              # Độ tin cậy (0.0 - 1.0)
    timestamp: int                 # Timestamp khi tạo signal
    price: float                   # Giá tại thời điểm tạo signal
    
    # Thông tin chi tiết về indicators
    indicators: Dict[str, float]   # Giá trị các indicators
    reasoning: str                 # Lý do tạo signal
    
    # Thông tin risk management
    stop_loss: Optional[float] = None      # Mức stop loss đề xuất
    take_profit: Optional[float] = None    # Mức take profit đề xuất
    risk_reward_ratio: Optional[float] = None  # Tỷ lệ risk/reward


class BaseIndicator(ABC, LoggerMixin):
    """
    Base class cho tất cả technical indicators
    
    Cung cấp interface chung và các utility methods cho indicators
    """
    
    def __init__(self, period: int = 14, name: str = "BaseIndicator"):
        """
        Khởi tạo base indicator
        
        Args:
            period: Chu kỳ tính toán (số periods)
            name: Tên của indicator
        """
        self.period = period
        self.name = name
        self.data_buffer: List[float] = []  # Buffer lưu trữ dữ liệu
        self.results: List[float] = []      # Kết quả tính toán
        
    @abstractmethod
    def calculate(self, price: float) -> Optional[float]:
        """
        Tính toán indicator với giá mới
        
        Args:
            price: Giá mới để tính toán
            
        Returns:
            Giá trị indicator hoặc None nếu chưa đủ dữ liệu
        """
        pass
    
    @abstractmethod
    def calculate_batch(self, prices: List[float]) -> List[float]:
        """
        Tính toán indicator cho một batch dữ liệu
        
        Args:
            prices: Danh sách giá để tính toán
            
        Returns:
            Danh sách kết quả indicator
        """
        pass
    
    def add_data(self, price: float) -> None:
        """
        Thêm dữ liệu mới vào buffer
        
        Args:
            price: Giá mới
        """
        self.data_buffer.append(price)
        
        # Giữ buffer trong giới hạn (tối đa 2x period để tính toán)
        max_buffer_size = max(self.period * 2, 100)
        if len(self.data_buffer) > max_buffer_size:
            self.data_buffer = self.data_buffer[-max_buffer_size:]
    
    def has_enough_data(self) -> bool:
        """
        Kiểm tra xem có đủ dữ liệu để tính toán không
        
        Returns:
            True nếu có đủ dữ liệu
        """
        return len(self.data_buffer) >= self.period
    
    def get_latest_value(self) -> Optional[float]:
        """
        Lấy giá trị indicator mới nhất
        
        Returns:
            Giá trị mới nhất hoặc None nếu chưa có
        """
        return self.results[-1] if self.results else None
    
    def reset(self) -> None:
        """Reset indicator về trạng thái ban đầu"""
        self.data_buffer.clear()
        self.results.clear()


class MovingAverage(BaseIndicator):
    """
    Moving Average Indicator - Đường trung bình động
    
    Hỗ trợ cả Simple Moving Average (SMA) và Exponential Moving Average (EMA)
    """
    
    def __init__(self, period: int = 20, ma_type: str = "SMA"):
        """
        Khởi tạo Moving Average
        
        Args:
            period: Chu kỳ tính toán
            ma_type: Loại MA ("SMA" hoặc "EMA")
        """
        super().__init__(period, f"{ma_type}_{period}")
        self.ma_type = ma_type.upper()
        self.alpha = 2.0 / (period + 1)  # Smoothing factor cho EMA
        self.ema_value = None  # Giá trị EMA hiện tại
        
    def calculate(self, price: float) -> Optional[float]:
        """
        Tính toán Moving Average với giá mới
        
        Args:
            price: Giá mới
            
        Returns:
            Giá trị MA hoặc None nếu chưa đủ dữ liệu
        """
        self.add_data(price)
        
        if not self.has_enough_data():
            return None
        
        if self.ma_type == "SMA":
            # Simple Moving Average
            ma_value = sum(self.data_buffer[-self.period:]) / self.period
        else:
            # Exponential Moving Average
            if self.ema_value is None:
                # Khởi tạo EMA với SMA của period đầu tiên
                self.ema_value = sum(self.data_buffer[-self.period:]) / self.period
            else:
                # Cập nhật EMA: EMA = α * price + (1-α) * EMA_previous
                self.ema_value = self.alpha * price + (1 - self.alpha) * self.ema_value
            
            ma_value = self.ema_value
        
        self.results.append(ma_value)
        return ma_value
    
    def calculate_batch(self, prices: List[float]) -> List[float]:
        """
        Tính toán MA cho batch dữ liệu
        
        Args:
            prices: Danh sách giá
            
        Returns:
            Danh sách giá trị MA
        """
        results = []
        
        if self.ma_type == "SMA":
            # Tính SMA cho từng window
            for i in range(len(prices)):
                if i >= self.period - 1:
                    sma = sum(prices[i - self.period + 1:i + 1]) / self.period
                    results.append(sma)
                else:
                    results.append(None)
        else:
            # Tính EMA
            ema = None
            for i, price in enumerate(prices):
                if i >= self.period - 1:
                    if ema is None:
                        # Khởi tạo với SMA
                        ema = sum(prices[i - self.period + 1:i + 1]) / self.period
                    else:
                        ema = self.alpha * price + (1 - self.alpha) * ema
                    results.append(ema)
                else:
                    results.append(None)
        
        return results


class RSI(BaseIndicator):
    """
    RSI (Relative Strength Index) Indicator
    
    Oscillator momentum indicator đo tốc độ và thay đổi của price movements
    Giá trị từ 0-100, thường dùng 70 (overbought) và 30 (oversold)
    """
    
    def __init__(self, period: int = 14, overbought: float = 70.0, oversold: float = 30.0):
        """
        Khởi tạo RSI indicator
        
        Args:
            period: Chu kỳ tính toán RSI
            overbought: Ngưỡng overbought (thường 70)
            oversold: Ngưỡng oversold (thường 30)
        """
        super().__init__(period, f"RSI_{period}")
        self.overbought = overbought
        self.oversold = oversold
        
        # Lưu trữ gains và losses để tính RS
        self.gains: List[float] = []
        self.losses: List[float] = []
        self.avg_gain = 0.0
        self.avg_loss = 0.0
        
    def calculate(self, price: float) -> Optional[float]:
        """
        Tính toán RSI với giá mới
        
        Args:
            price: Giá mới
            
        Returns:
            Giá trị RSI (0-100) hoặc None nếu chưa đủ dữ liệu
        """
        self.add_data(price)
        
        if len(self.data_buffer) < 2:
            return None
        
        # Tính price change
        price_change = self.data_buffer[-1] - self.data_buffer[-2]
        
        # Phân loại thành gain hoặc loss
        gain = max(price_change, 0)
        loss = abs(min(price_change, 0))
        
        self.gains.append(gain)
        self.losses.append(loss)
        
        # Giữ buffer trong giới hạn
        if len(self.gains) > self.period * 2:
            self.gains = self.gains[-self.period * 2:]
            self.losses = self.losses[-self.period * 2:]
        
        if len(self.gains) < self.period:
            return None
        
        # Tính average gain và loss
        if len(self.gains) == self.period:
            # Lần đầu tiên: dùng simple average
            self.avg_gain = sum(self.gains[-self.period:]) / self.period
            self.avg_loss = sum(self.losses[-self.period:]) / self.period
        else:
            # Các lần sau: dùng smoothed average (Wilder's smoothing)
            self.avg_gain = ((self.avg_gain * (self.period - 1)) + gain) / self.period
            self.avg_loss = ((self.avg_loss * (self.period - 1)) + loss) / self.period
        
        # Tính RSI
        if self.avg_loss == 0:
            rsi = 100.0  # Không có loss nào
        else:
            rs = self.avg_gain / self.avg_loss
            rsi = 100.0 - (100.0 / (1.0 + rs))
        
        self.results.append(rsi)
        return rsi
    
    def calculate_batch(self, prices: List[float]) -> List[float]:
        """
        Tính toán RSI cho batch dữ liệu
        
        Args:
            prices: Danh sách giá
            
        Returns:
            Danh sách giá trị RSI
        """
        if len(prices) < self.period + 1:
            return [None] * len(prices)
        
        results = [None] * len(prices)
        gains = []
        losses = []
        
        # Tính price changes
        for i in range(1, len(prices)):
            change = prices[i] - prices[i-1]
            gains.append(max(change, 0))
            losses.append(abs(min(change, 0)))
        
        # Tính RSI cho từng điểm
        for i in range(self.period - 1, len(gains)):
            if i == self.period - 1:
                # Lần đầu: simple average
                avg_gain = sum(gains[i - self.period + 1:i + 1]) / self.period
                avg_loss = sum(losses[i - self.period + 1:i + 1]) / self.period
            else:
                # Smoothed average
                avg_gain = ((avg_gain * (self.period - 1)) + gains[i]) / self.period
                avg_loss = ((avg_loss * (self.period - 1)) + losses[i]) / self.period
            
            # Tính RSI
            if avg_loss == 0:
                rsi = 100.0
            else:
                rs = avg_gain / avg_loss
                rsi = 100.0 - (100.0 / (1.0 + rs))
            
            results[i + 1] = rsi  # +1 vì bắt đầu từ index 1
        
        return results
    
    def is_overbought(self) -> bool:
        """Kiểm tra xem RSI có ở vùng overbought không"""
        latest = self.get_latest_value()
        return latest is not None and latest > self.overbought

    def is_oversold(self) -> bool:
        """Kiểm tra xem RSI có ở vùng oversold không"""
        latest = self.get_latest_value()
        return latest is not None and latest < self.oversold


@dataclass
class MACDResult:
    """Kết quả tính toán MACD"""
    macd_line: float      # MACD line (fast EMA - slow EMA)
    signal_line: float    # Signal line (EMA của MACD line)
    histogram: float      # Histogram (MACD - Signal)


class MACD(BaseIndicator):
    """
    MACD (Moving Average Convergence Divergence) Indicator

    Trend-following momentum indicator sử dụng relationship giữa 2 moving averages
    Bao gồm: MACD line, Signal line, và Histogram
    """

    def __init__(self, fast_period: int = 12, slow_period: int = 26, signal_period: int = 9):
        """
        Khởi tạo MACD indicator

        Args:
            fast_period: Chu kỳ EMA nhanh (thường 12)
            slow_period: Chu kỳ EMA chậm (thường 26)
            signal_period: Chu kỳ EMA cho signal line (thường 9)
        """
        super().__init__(slow_period, f"MACD_{fast_period}_{slow_period}_{signal_period}")
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.signal_period = signal_period

        # Tạo EMA indicators
        self.fast_ema = MovingAverage(fast_period, "EMA")
        self.slow_ema = MovingAverage(slow_period, "EMA")
        self.signal_ema = MovingAverage(signal_period, "EMA")

        # Lưu trữ kết quả MACD
        self.macd_values: List[float] = []
        self.signal_values: List[float] = []
        self.histogram_values: List[float] = []

    def calculate(self, price: float) -> Optional[MACDResult]:
        """
        Tính toán MACD với giá mới

        Args:
            price: Giá mới

        Returns:
            MACDResult hoặc None nếu chưa đủ dữ liệu
        """
        # Tính fast và slow EMA
        fast_ema = self.fast_ema.calculate(price)
        slow_ema = self.slow_ema.calculate(price)

        if fast_ema is None or slow_ema is None:
            return None

        # Tính MACD line
        macd_line = fast_ema - slow_ema
        self.macd_values.append(macd_line)

        # Tính Signal line (EMA của MACD line)
        signal_line = self.signal_ema.calculate(macd_line)

        if signal_line is None:
            return None

        self.signal_values.append(signal_line)

        # Tính Histogram
        histogram = macd_line - signal_line
        self.histogram_values.append(histogram)

        # Giữ buffer trong giới hạn
        max_size = self.period * 2
        if len(self.macd_values) > max_size:
            self.macd_values = self.macd_values[-max_size:]
            self.signal_values = self.signal_values[-max_size:]
            self.histogram_values = self.histogram_values[-max_size:]

        result = MACDResult(
            macd_line=macd_line,
            signal_line=signal_line,
            histogram=histogram
        )

        self.results.append(result)
        return result

    def calculate_batch(self, prices: List[float]) -> List[Optional[MACDResult]]:
        """
        Tính toán MACD cho batch dữ liệu

        Args:
            prices: Danh sách giá

        Returns:
            Danh sách MACDResult
        """
        # Tính EMA values
        fast_emas = self.fast_ema.calculate_batch(prices)
        slow_emas = self.slow_ema.calculate_batch(prices)

        results = []
        macd_lines = []

        # Tính MACD lines
        for fast, slow in zip(fast_emas, slow_emas):
            if fast is not None and slow is not None:
                macd_line = fast - slow
                macd_lines.append(macd_line)
            else:
                macd_lines.append(None)

        # Tính Signal lines
        signal_lines = self.signal_ema.calculate_batch([m for m in macd_lines if m is not None])

        # Combine results
        signal_idx = 0
        for i, macd_line in enumerate(macd_lines):
            if macd_line is not None and signal_idx < len(signal_lines) and signal_lines[signal_idx] is not None:
                signal_line = signal_lines[signal_idx]
                histogram = macd_line - signal_line

                results.append(MACDResult(
                    macd_line=macd_line,
                    signal_line=signal_line,
                    histogram=histogram
                ))
                signal_idx += 1
            else:
                results.append(None)

        return results

    def get_latest_result(self) -> Optional[MACDResult]:
        """Lấy kết quả MACD mới nhất"""
        return self.results[-1] if self.results else None

    def is_bullish_crossover(self) -> bool:
        """Kiểm tra MACD line có cross above signal line không"""
        if len(self.results) < 2:
            return False

        current = self.results[-1]
        previous = self.results[-2]

        return (previous.macd_line <= previous.signal_line and
                current.macd_line > current.signal_line)

    def is_bearish_crossover(self) -> bool:
        """Kiểm tra MACD line có cross below signal line không"""
        if len(self.results) < 2:
            return False

        current = self.results[-1]
        previous = self.results[-2]

        return (previous.macd_line >= previous.signal_line and
                current.macd_line < current.signal_line)


@dataclass
class BollingerBandsResult:
    """Kết quả tính toán Bollinger Bands"""
    upper_band: float     # Upper band (SMA + 2*std)
    middle_band: float    # Middle band (SMA)
    lower_band: float     # Lower band (SMA - 2*std)
    bandwidth: float      # Bandwidth (upper - lower) / middle
    percent_b: float      # %B position trong bands


class BollingerBands(BaseIndicator):
    """
    Bollinger Bands Indicator

    Volatility indicator gồm 3 bands:
    - Middle band: Simple Moving Average
    - Upper band: SMA + (standard deviation * multiplier)
    - Lower band: SMA - (standard deviation * multiplier)
    """

    def __init__(self, period: int = 20, std_multiplier: float = 2.0):
        """
        Khởi tạo Bollinger Bands

        Args:
            period: Chu kỳ tính toán SMA và standard deviation
            std_multiplier: Hệ số nhân với standard deviation (thường 2.0)
        """
        super().__init__(period, f"BB_{period}_{std_multiplier}")
        self.std_multiplier = std_multiplier
        self.sma = MovingAverage(period, "SMA")

    def calculate(self, price: float) -> Optional[BollingerBandsResult]:
        """
        Tính toán Bollinger Bands với giá mới

        Args:
            price: Giá mới

        Returns:
            BollingerBandsResult hoặc None nếu chưa đủ dữ liệu
        """
        self.add_data(price)

        if not self.has_enough_data():
            return None

        # Tính SMA (middle band)
        middle_band = self.sma.calculate(price)
        if middle_band is None:
            return None

        # Tính standard deviation
        recent_prices = self.data_buffer[-self.period:]
        std_dev = np.std(recent_prices, ddof=0)  # Population standard deviation

        # Tính upper và lower bands
        upper_band = middle_band + (self.std_multiplier * std_dev)
        lower_band = middle_band - (self.std_multiplier * std_dev)

        # Tính bandwidth
        bandwidth = (upper_band - lower_band) / middle_band if middle_band != 0 else 0

        # Tính %B (vị trí của price trong bands)
        if upper_band != lower_band:
            percent_b = (price - lower_band) / (upper_band - lower_band)
        else:
            percent_b = 0.5  # Giữa bands nếu không có volatility

        result = BollingerBandsResult(
            upper_band=upper_band,
            middle_band=middle_band,
            lower_band=lower_band,
            bandwidth=bandwidth,
            percent_b=percent_b
        )

        self.results.append(result)
        return result

    def calculate_batch(self, prices: List[float]) -> List[Optional[BollingerBandsResult]]:
        """
        Tính toán Bollinger Bands cho batch dữ liệu

        Args:
            prices: Danh sách giá

        Returns:
            Danh sách BollingerBandsResult
        """
        results = []
        sma_values = self.sma.calculate_batch(prices)

        for i, (price, sma) in enumerate(zip(prices, sma_values)):
            if sma is not None and i >= self.period - 1:
                # Tính standard deviation cho window hiện tại
                window_prices = prices[i - self.period + 1:i + 1]
                std_dev = np.std(window_prices, ddof=0)

                # Tính bands
                upper_band = sma + (self.std_multiplier * std_dev)
                lower_band = sma - (self.std_multiplier * std_dev)

                # Tính bandwidth và %B
                bandwidth = (upper_band - lower_band) / sma if sma != 0 else 0

                if upper_band != lower_band:
                    percent_b = (price - lower_band) / (upper_band - lower_band)
                else:
                    percent_b = 0.5

                results.append(BollingerBandsResult(
                    upper_band=upper_band,
                    middle_band=sma,
                    lower_band=lower_band,
                    bandwidth=bandwidth,
                    percent_b=percent_b
                ))
            else:
                results.append(None)

        return results

    def get_latest_result(self) -> Optional[BollingerBandsResult]:
        """Lấy kết quả Bollinger Bands mới nhất"""
        return self.results[-1] if self.results else None

    def is_squeeze(self, threshold: float = 0.1) -> bool:
        """
        Kiểm tra xem có đang trong trạng thái squeeze không
        (bandwidth thấp - volatility thấp)
        """
        latest = self.get_latest_result()
        return latest is not None and latest.bandwidth < threshold

    def is_price_above_upper_band(self) -> bool:
        """Kiểm tra giá có trên upper band không"""
        if not self.data_buffer or not self.results:
            return False

        latest_price = self.data_buffer[-1]
        latest_result = self.results[-1]
        return latest_price > latest_result.upper_band

    def is_price_below_lower_band(self) -> bool:
        """Kiểm tra giá có dưới lower band không"""
        if not self.data_buffer or not self.results:
            return False

        latest_price = self.data_buffer[-1]
        latest_result = self.results[-1]
        return latest_price < latest_result.lower_band


class VolumeMovingAverage(BaseIndicator):
    """
    Volume Moving Average - Trung bình động của volume

    Sử dụng để xác định volume trung bình và so sánh với volume hiện tại
    """

    def __init__(self, period: int = 20):
        """
        Khởi tạo Volume Moving Average

        Args:
            period: Chu kỳ tính toán volume MA
        """
        super().__init__(period, f"VMA_{period}")
        self.volume_ma = MovingAverage(period, "SMA")

    def calculate(self, volume: float) -> Optional[float]:
        """
        Tính toán Volume MA với volume mới

        Args:
            volume: Volume mới

        Returns:
            Volume MA hoặc None nếu chưa đủ dữ liệu
        """
        return self.volume_ma.calculate(volume)

    def calculate_batch(self, volumes: List[float]) -> List[float]:
        """
        Tính toán Volume MA cho batch dữ liệu

        Args:
            volumes: Danh sách volume

        Returns:
            Danh sách Volume MA
        """
        return self.volume_ma.calculate_batch(volumes)


class VolumeRateOfChange(BaseIndicator):
    """
    Volume Rate of Change (VROC) - Tỷ lệ thay đổi volume

    Đo lường tốc độ thay đổi volume so với n periods trước
    """

    def __init__(self, period: int = 10):
        """
        Khởi tạo Volume Rate of Change

        Args:
            period: Chu kỳ so sánh volume
        """
        super().__init__(period, f"VROC_{period}")
        self.volumes: List[float] = []

    def calculate(self, volume: float) -> Optional[float]:
        """
        Tính toán VROC với volume mới

        Args:
            volume: Volume mới

        Returns:
            VROC (%) hoặc None nếu chưa đủ dữ liệu
        """
        self.volumes.append(volume)

        # Giữ buffer trong giới hạn
        if len(self.volumes) > self.period * 2:
            self.volumes = self.volumes[-self.period * 2:]

        if len(self.volumes) <= self.period:
            return None

        # Tính VROC: ((current_volume - volume_n_periods_ago) / volume_n_periods_ago) * 100
        current_volume = self.volumes[-1]
        past_volume = self.volumes[-self.period - 1]

        if past_volume == 0:
            return 0.0

        vroc = ((current_volume - past_volume) / past_volume) * 100
        self.results.append(vroc)
        return vroc

    def calculate_batch(self, volumes: List[float]) -> List[float]:
        """
        Tính toán VROC cho batch dữ liệu

        Args:
            volumes: Danh sách volume

        Returns:
            Danh sách VROC
        """
        results = [None] * len(volumes)

        for i in range(self.period, len(volumes)):
            current_volume = volumes[i]
            past_volume = volumes[i - self.period]

            if past_volume == 0:
                vroc = 0.0
            else:
                vroc = ((current_volume - past_volume) / past_volume) * 100

            results[i] = vroc

        return results


class OnBalanceVolume(BaseIndicator):
    """
    On-Balance Volume (OBV) - Volume cân bằng

    Momentum indicator sử dụng volume flow để predict price changes
    """

    def __init__(self):
        """Khởi tạo OBV indicator"""
        super().__init__(1, "OBV")  # OBV không cần period
        self.obv_value = 0.0
        self.previous_price = None

    def calculate(self, price: float, volume: float) -> float:
        """
        Tính toán OBV với giá và volume mới

        Args:
            price: Giá mới
            volume: Volume mới

        Returns:
            Giá trị OBV
        """
        if self.previous_price is None:
            self.previous_price = price
            return self.obv_value

        # Cập nhật OBV dựa trên price direction
        if price > self.previous_price:
            # Giá tăng: thêm volume
            self.obv_value += volume
        elif price < self.previous_price:
            # Giá giảm: trừ volume
            self.obv_value -= volume
        # Giá không đổi: OBV không thay đổi

        self.previous_price = price
        self.results.append(self.obv_value)
        return self.obv_value

    def calculate_batch(self, prices: List[float], volumes: List[float]) -> List[float]:
        """
        Tính toán OBV cho batch dữ liệu

        Args:
            prices: Danh sách giá
            volumes: Danh sách volume

        Returns:
            Danh sách OBV values
        """
        if len(prices) != len(volumes):
            raise ValueError("Prices và volumes phải có cùng độ dài")

        results = []
        obv = 0.0

        for i, (price, volume) in enumerate(zip(prices, volumes)):
            if i == 0:
                results.append(obv)
                continue

            prev_price = prices[i - 1]

            if price > prev_price:
                obv += volume
            elif price < prev_price:
                obv -= volume

            results.append(obv)

        return results

    def calculate(self, price: float) -> Optional[float]:
        """Override base method - OBV cần cả price và volume"""
        raise NotImplementedError("OBV cần cả price và volume. Sử dụng calculate(price, volume)")

    def calculate_batch(self, prices: List[float]) -> List[float]:
        """Override base method - OBV cần cả prices và volumes"""
        raise NotImplementedError("OBV cần cả prices và volumes. Sử dụng calculate_batch(prices, volumes)")
