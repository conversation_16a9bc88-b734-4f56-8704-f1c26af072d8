"""
Demo đơn giản để test Technical Analysis mà không cần tất cả dependencies
Chỉ sử dụng built-in Python libraries và một số packages cơ bản
"""
import asyncio
import json
import time
import math
from datetime import datetime
from typing import List, Optional, Dict, Any


class SimpleRSI:
    """RSI đơn giản chỉ dùng built-in Python"""
    
    def __init__(self, period: int = 14):
        self.period = period
        self.prices: List[float] = []
        self.gains: List[float] = []
        self.losses: List[float] = []
        
    def calculate(self, price: float) -> Optional[float]:
        """Tính RSI với giá mới"""
        self.prices.append(price)
        
        if len(self.prices) < 2:
            return None
        
        # Tính price change
        change = self.prices[-1] - self.prices[-2]
        gain = max(change, 0)
        loss = abs(min(change, 0))
        
        self.gains.append(gain)
        self.losses.append(loss)
        
        # Giữ buffer trong giới hạn
        if len(self.gains) > self.period * 2:
            self.gains = self.gains[-self.period:]
            self.losses = self.losses[-self.period:]
        
        if len(self.gains) < self.period:
            return None
        
        # Tính average gain và loss
        avg_gain = sum(self.gains[-self.period:]) / self.period
        avg_loss = sum(self.losses[-self.period:]) / self.period
        
        if avg_loss == 0:
            return 100.0
        
        rs = avg_gain / avg_loss
        rsi = 100.0 - (100.0 / (1.0 + rs))
        
        return rsi


class SimpleEMA:
    """EMA đơn giản"""
    
    def __init__(self, period: int):
        self.period = period
        self.alpha = 2.0 / (period + 1)
        self.ema_value = None
        self.count = 0
        
    def calculate(self, price: float) -> Optional[float]:
        """Tính EMA với giá mới"""
        self.count += 1
        
        if self.ema_value is None:
            self.ema_value = price
        else:
            self.ema_value = self.alpha * price + (1 - self.alpha) * self.ema_value
        
        # Chỉ return sau khi có đủ data
        if self.count >= self.period:
            return self.ema_value
        
        return None


class SimpleSignalGenerator:
    """Signal generator đơn giản"""
    
    def __init__(self):
        self.rsi = SimpleRSI(14)
        self.ema_9 = SimpleEMA(9)
        self.ema_21 = SimpleEMA(21)
        self.signals_generated = 0
        
    def process_price(self, symbol: str, price: float) -> Optional[Dict[str, Any]]:
        """Xử lý giá mới và tạo signal nếu có"""
        
        # Tính indicators
        rsi_value = self.rsi.calculate(price)
        ema_9_value = self.ema_9.calculate(price)
        ema_21_value = self.ema_21.calculate(price)
        
        # Chỉ tạo signal khi có đủ data
        if not all([rsi_value, ema_9_value, ema_21_value]):
            return None
        
        # Phân tích signal
        signal_type = "HOLD"
        confidence = 0.5
        reasoning = []
        
        # RSI analysis
        if rsi_value < 30:
            signal_type = "BUY"
            confidence += 0.2
            reasoning.append(f"RSI oversold ({rsi_value:.1f})")
        elif rsi_value > 70:
            signal_type = "SELL"
            confidence += 0.2
            reasoning.append(f"RSI overbought ({rsi_value:.1f})")
        else:
            reasoning.append(f"RSI neutral ({rsi_value:.1f})")
        
        # EMA trend analysis
        if ema_9_value > ema_21_value:
            if signal_type == "HOLD":
                signal_type = "BUY"
            confidence += 0.15
            reasoning.append("EMA 9 > EMA 21 (uptrend)")
        elif ema_9_value < ema_21_value:
            if signal_type == "HOLD":
                signal_type = "SELL"
            confidence += 0.15
            reasoning.append("EMA 9 < EMA 21 (downtrend)")
        
        # Chỉ tạo signal nếu confidence đủ cao
        if confidence < 0.6:
            return None
        
        self.signals_generated += 1
        
        return {
            "signal_id": self.signals_generated,
            "symbol": symbol,
            "signal_type": signal_type,
            "confidence": min(confidence, 1.0),
            "price": price,
            "timestamp": datetime.now().strftime("%H:%M:%S"),
            "indicators": {
                "rsi": rsi_value,
                "ema_9": ema_9_value,
                "ema_21": ema_21_value
            },
            "reasoning": "; ".join(reasoning)
        }


def generate_mock_price_data(base_price: float, volatility: float = 0.02) -> float:
    """Tạo mock price data với random walk"""
    # Simple random walk với trend
    change_percent = (math.sin(time.time() / 10) * 0.01 + 
                     (hash(str(time.time())) % 1000 - 500) / 50000 * volatility)
    
    new_price = base_price * (1 + change_percent)
    return round(new_price, 2)


async def run_simple_demo():
    """Chạy demo đơn giản"""
    print("🚀 DEMO TECHNICAL ANALYSIS ĐƠN GIẢN")
    print("=" * 60)
    print("Demo này sử dụng:")
    print("✓ RSI (14 periods)")
    print("✓ EMA 9 và EMA 21")
    print("✓ Signal generation với confidence scoring")
    print("✓ Mock price data (không cần API)")
    print("=" * 60)
    
    # Khởi tạo
    btc_generator = SimpleSignalGenerator()
    eth_generator = SimpleSignalGenerator()
    
    # Mock prices
    btc_price = 43000.0
    eth_price = 2500.0
    
    demo_duration = 60  # 1 phút demo
    update_interval = 2  # Update mỗi 2 giây
    
    start_time = time.time()
    update_count = 0
    
    print(f"Bắt đầu demo trong {demo_duration} giây...")
    print("Generating mock price movements...\n")
    
    try:
        while time.time() - start_time < demo_duration:
            update_count += 1
            timestamp = datetime.now().strftime("%H:%M:%S")
            
            # Generate new prices
            btc_price = generate_mock_price_data(btc_price, 0.02)
            eth_price = generate_mock_price_data(eth_price, 0.025)
            
            print(f"[{timestamp}] Update #{update_count}")
            print(f"  BTC: ${btc_price:,.2f}")
            print(f"  ETH: ${eth_price:,.2f}")
            
            # Process BTC
            btc_signal = btc_generator.process_price("BTCUSDT", btc_price)
            if btc_signal:
                print(f"\n🎯 BTC SIGNAL:")
                print(f"  Type: {btc_signal['signal_type']}")
                print(f"  Confidence: {btc_signal['confidence']:.1%}")
                print(f"  RSI: {btc_signal['indicators']['rsi']:.1f}")
                print(f"  EMA 9: ${btc_signal['indicators']['ema_9']:.2f}")
                print(f"  EMA 21: ${btc_signal['indicators']['ema_21']:.2f}")
                print(f"  Reasoning: {btc_signal['reasoning']}")
            
            # Process ETH
            eth_signal = eth_generator.process_price("ETHUSDT", eth_price)
            if eth_signal:
                print(f"\n📈 ETH SIGNAL:")
                print(f"  Type: {eth_signal['signal_type']}")
                print(f"  Confidence: {eth_signal['confidence']:.1%}")
                print(f"  RSI: {eth_signal['indicators']['rsi']:.1f}")
                print(f"  EMA 9: ${eth_signal['indicators']['ema_9']:.2f}")
                print(f"  EMA 21: ${eth_signal['indicators']['ema_21']:.2f}")
                print(f"  Reasoning: {eth_signal['reasoning']}")
            
            print("-" * 40)
            
            # Wait
            await asyncio.sleep(update_interval)
    
    except KeyboardInterrupt:
        print("\n⏹️  Demo stopped by user")
    
    # Summary
    print(f"\n📊 DEMO SUMMARY:")
    print(f"  Duration: {time.time() - start_time:.1f} seconds")
    print(f"  Updates: {update_count}")
    print(f"  BTC Signals: {btc_generator.signals_generated}")
    print(f"  ETH Signals: {eth_generator.signals_generated}")
    print(f"  Total Signals: {btc_generator.signals_generated + eth_generator.signals_generated}")
    
    print(f"\n🎉 Demo completed successfully!")
    print("Technical Analysis logic working correctly.")


def test_indicators_standalone():
    """Test indicators standalone"""
    print("\n🔬 TESTING INDICATORS STANDALONE")
    print("=" * 50)
    
    # Test RSI
    rsi = SimpleRSI(5)  # Short period for quick demo
    test_prices = [100, 102, 101, 103, 105, 104, 106, 108, 107, 109, 111, 110]
    
    print("RSI Test:")
    for i, price in enumerate(test_prices):
        rsi_value = rsi.calculate(price)
        if rsi_value:
            print(f"  Price: ${price} -> RSI: {rsi_value:.1f}")
    
    # Test EMA
    ema = SimpleEMA(5)
    print(f"\nEMA Test:")
    for i, price in enumerate(test_prices):
        ema_value = ema.calculate(price)
        if ema_value:
            print(f"  Price: ${price} -> EMA: {ema_value:.2f}")
    
    print("✅ Indicators working correctly!")


async def main():
    """Main function"""
    print("🔬 CRYPTO TRADING BOT - SIMPLE TECHNICAL ANALYSIS DEMO")
    print("=" * 70)
    
    # Test indicators first
    test_indicators_standalone()
    
    # Run main demo
    await run_simple_demo()


if __name__ == "__main__":
    print("Chạy demo đơn giản (không cần external dependencies)")
    print("Nhấn Ctrl+C để dừng demo sớm")
    print()
    
    asyncio.run(main())
