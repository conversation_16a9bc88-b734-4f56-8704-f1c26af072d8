"""
Test extreme conditions de force BUY/SELL signals
"""
import asyncio
from datetime import datetime, timedelta

from app.core.signal_generator import SignalGenerator, IndicatorConfig
from app.core.data_collector import MarketData
from app.utils.logger import setup_logging, get_logger

setup_logging("INFO")
logger = get_logger(__name__)


async def test_extreme_signals():
    """Test voi extreme conditions"""
    logger.info("=== TEST EXTREME SIGNALS ===")
    
    # Cau hinh rat thap
    config = IndicatorConfig(
        rsi_period=5,
        rsi_overbought=55,  # Rat thap
        rsi_oversold=45,   # Rat cao
        ma_fast_period=3,
        ma_medium_period=5,
        ma_slow_period=10,
        min_confidence=0.1,  # Rat thap
        max_signals_per_hour=50
    )
    
    signal_gen = SignalGenerator(config)
    
    # Test 1: Extreme downtrend -> should create BUY signal
    logger.info("\n1. EXTREME DOWNTREND TEST:")
    prices_down = [100, 95, 90, 85, 80, 75, 70, 65, 60, 55, 50, 45, 40, 35, 30]
    
    for i, price in enumerate(prices_down):
        market_data = MarketData(
            symbol="TESTUSDT",
            timestamp=int((datetime.now() + timedelta(hours=i)).timestamp() * 1000),
            source="test",
            close=price,
            volume=1000.0,
            timeframe="1h"
        )
        
        signal = signal_gen.process_market_data(market_data)
        
        if signal and signal.signal_type.value != "HOLD":
            logger.info(f"SIGNAL: {signal.signal_type.value} at ${price} "
                       f"(confidence: {signal.confidence:.2f})")
            logger.info(f"  Reasoning: {signal.reasoning}")
        
        # Hien thi indicators
        indicators = signal_gen.get_latest_indicators()
        if indicators and i >= 10:  # Sau khi co du data
            logger.info(f"Price: ${price}, RSI: {indicators.rsi:.1f}")
    
    # Reset va test uptrend
    signal_gen.reset()
    
    # Test 2: Extreme uptrend -> should create SELL signal
    logger.info("\n2. EXTREME UPTREND TEST:")
    prices_up = [30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95, 100]
    
    for i, price in enumerate(prices_up):
        market_data = MarketData(
            symbol="TESTUSDT",
            timestamp=int((datetime.now() + timedelta(hours=i)).timestamp() * 1000),
            source="test",
            close=price,
            volume=1000.0,
            timeframe="1h"
        )
        
        signal = signal_gen.process_market_data(market_data)
        
        if signal and signal.signal_type.value != "HOLD":
            logger.info(f"SIGNAL: {signal.signal_type.value} at ${price} "
                       f"(confidence: {signal.confidence:.2f})")
            logger.info(f"  Reasoning: {signal.reasoning}")
        
        # Hien thi indicators
        indicators = signal_gen.get_latest_indicators()
        if indicators and i >= 10:
            logger.info(f"Price: ${price}, RSI: {indicators.rsi:.1f}")
    
    # Test 3: Oscillating market
    logger.info("\n3. OSCILLATING MARKET TEST:")
    signal_gen.reset()
    
    prices_osc = [50, 45, 55, 40, 60, 35, 65, 30, 70, 25, 75, 20, 80, 15, 85]
    
    for i, price in enumerate(prices_osc):
        market_data = MarketData(
            symbol="TESTUSDT",
            timestamp=int((datetime.now() + timedelta(hours=i)).timestamp() * 1000),
            source="test",
            close=price,
            volume=1000.0,
            timeframe="1h"
        )
        
        signal = signal_gen.process_market_data(market_data)
        
        if signal:
            logger.info(f"SIGNAL: {signal.signal_type.value} at ${price} "
                       f"(confidence: {signal.confidence:.2f})")
            if signal.signal_type.value != "HOLD":
                logger.info(f"  *** NON-HOLD SIGNAL! *** {signal.reasoning}")


async def test_manual_score_calculation():
    """Test manual score calculation"""
    logger.info("\n=== MANUAL SCORE CALCULATION ===")
    
    config = IndicatorConfig(min_confidence=0.1)
    signal_gen = SignalGenerator(config)
    
    # Tao mock indicators
    from app.core.signal_generator import IndicatorValues
    
    # Test case 1: Strong bullish
    indicators = IndicatorValues(
        price=50000.0,
        volume=1000.0,
        rsi=25.0,  # Oversold
        ema_9=50100.0,  # Above price
        ema_21=49900.0,  # Below EMA 9
        ema_50=49800.0,
        macd_line=100.0,  # Bullish
        macd_signal=50.0,
        volume_ma=800.0  # High volume
    )
    
    # Manual calculation
    trend_score = signal_gen._analyze_trend(indicators)
    momentum_score = signal_gen._analyze_momentum(indicators)
    volume_score = signal_gen._analyze_volume(indicators)
    volatility_score = signal_gen._analyze_volatility(indicators)
    
    total_score = (trend_score + momentum_score + volume_score + volatility_score) / 4
    
    logger.info(f"Manual calculation:")
    logger.info(f"  Trend score: {trend_score:.3f}")
    logger.info(f"  Momentum score: {momentum_score:.3f}")
    logger.info(f"  Volume score: {volume_score:.3f}")
    logger.info(f"  Volatility score: {volatility_score:.3f}")
    logger.info(f"  Total score: {total_score:.3f}")
    
    signal_type = signal_gen._determine_signal_type(total_score, indicators)
    logger.info(f"  Signal type: {signal_type.value}")
    
    if abs(total_score) >= config.min_confidence:
        logger.info(f"  -> SHOULD CREATE SIGNAL (score: {total_score:.3f} >= {config.min_confidence})")
    else:
        logger.info(f"  -> NO SIGNAL (score: {total_score:.3f} < {config.min_confidence})")


async def main():
    await test_extreme_signals()
    await test_manual_score_calculation()


if __name__ == "__main__":
    asyncio.run(main())
